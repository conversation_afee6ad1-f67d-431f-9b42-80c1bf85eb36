import { customFetch, showErrors } from '@/lib/utils';
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { toast } from 'sonner';

const initialState = {
	currentLogStatus: null,
	userProjectDetails: null,
	userProjectCalender: [],
	projectLogs: [],
	attendanceLogs: [],
	timeSheets: [],
	timeSheet: null,
	isLoadingProject: false,
	isLoading: false,
	currentProject: '',
	shiftDetails: null,
};

export const fetchAssignedShiftDetails = createAsyncThunk(
	'/attendance/fetchAssignedShiftDetails',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/shifts/assigned');
			if (data.success) {
				thunkAPI.dispatch(getAttendanceLogs());
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const getAttendanceProjectLogs = createAsyncThunk(
	'/attendance/getAttendanceProjectLogs',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/attendance/project-logs');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const getAttendanceUserProjectLogs = createAsyncThunk(
	'/attendance/getAttendanceUserProjectLogs',
	async (userId, thunkAPI) => {
		try {
			const { data } = await customFetch.get(
				`/attendance/project-logs/${userId}`
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const getAttendanceUserProjectCalender = createAsyncThunk(
	'/attendance/getAttendanceUserProjectCalender',
	async ({ userId, projectId }, thunkAPI) => {
		try {
			const { data } = await customFetch.get(
				`/attendance/project-logs/${userId}/calender/${projectId}`
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const getAttendanceLogs = createAsyncThunk(
	'/attendance/getAttendanceLogs',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/attendance/logs');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const getStatus = createAsyncThunk(
	'/attendance/getStatus',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/attendance/status');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const clockIn = createAsyncThunk(
	'/attendance/clockIn',
	async (clockInDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/attendance/clock-in',
				clockInDetails
			);
			if (data.success) {
				thunkAPI.dispatch(getAttendanceLogs());
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const startBreak = createAsyncThunk(
	'/attendance/startBreak',
	async (startBreakDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/attendance/start-break',
				startBreakDetails
			);
			if (data.success) {
				thunkAPI.dispatch(getAttendanceLogs());
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const endBreak = createAsyncThunk(
	'/attendance/endBreak',
	async (endBreakDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/attendance/end-break',
				endBreakDetails
			);
			if (data.success) {
				thunkAPI.dispatch(getAttendanceLogs());
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const clockOut = createAsyncThunk(
	'/attendance/clockOut',
	async (clockOutDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/attendance/clock-out',
				clockOutDetails
			);
			if (data.success) {
				thunkAPI.dispatch(getAttendanceLogs());
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchTimeSheet = createAsyncThunk(
	'/attendance/fetchTimeSheet',
	async ({ month } = {}, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/attendance/time-sheet', {
				params: {
					month,
				},
			});
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchTimeSheetByUserId = createAsyncThunk(
	'/attendance/fetchTimeSheetByUserId',
	async ({ month, userId } = {}, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/attendance/time-sheet', {
				params: {
					month,
					userId,
				},
			});
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const switchProject = createAsyncThunk(
	'/attendance/switchProject',
	async (switchProjectDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/attendance/switch-project',
				switchProjectDetails
			);
			if (data.success) {
				thunkAPI.dispatch(getAttendanceLogs());
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const attendanceSlice = createSlice({
	name: 'attendance',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(getAttendanceLogs.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(getAttendanceLogs.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.attendanceLogs = payload?.data?.attendanceTime;
				state.clockInDelay = payload?.data?.clockInDelay;
				state.clockOutDelay = payload?.data?.clockOutDelay;
				state.clockInLimit = payload?.data?.clockInLimit;
				state.clockOutLimit = payload?.data?.clockOutLimit;
				state.currentProject = payload?.data?.project?.name;
			})
			.addCase(getAttendanceLogs.rejected, (state, { payload }) => {
				state.isLoading = false;

				// showErrors(payload);
			})
			.addCase(getAttendanceProjectLogs.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(getAttendanceProjectLogs.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.projectLogs = payload.data.employees;
			})
			.addCase(getAttendanceProjectLogs.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(getAttendanceUserProjectLogs.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(getAttendanceUserProjectLogs.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.userProjectDetails = payload.data;
			})
			.addCase(getAttendanceUserProjectLogs.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(getAttendanceUserProjectCalender.pending, (state) => {
				state.isLoadingProject = true;
			})
			.addCase(
				getAttendanceUserProjectCalender.fulfilled,
				(state, { payload }) => {
					state.isLoadingProject = false;
					state.userProjectCalender = payload.data;
				}
			)
			.addCase(
				getAttendanceUserProjectCalender.rejected,
				(state, { payload }) => {
					state.isLoadingProject = false;
					showErrors(payload);
				}
			)
			.addCase(getStatus.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(getStatus.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.currentLogStatus = payload.data;
			})
			.addCase(getStatus.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(clockIn.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(clockIn.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.currentLogStatus = payload.data;
				toast.success(payload.message);
			})
			.addCase(clockIn.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(startBreak.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(startBreak.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.currentLogStatus = payload.data;
				toast.success(payload.message);
			})
			.addCase(startBreak.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(endBreak.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(endBreak.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.currentLogStatus = payload.data;
				toast.success(payload.message);
			})
			.addCase(endBreak.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(clockOut.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(clockOut.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.currentLogStatus = payload.data;
				toast.success(payload.message);
			})
			.addCase(clockOut.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchTimeSheet.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchTimeSheet.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.timeSheets = payload.data.timeSheet;
			})
			.addCase(fetchTimeSheet.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchTimeSheetByUserId.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchTimeSheetByUserId.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.timeSheet = payload.data;
			})
			.addCase(fetchTimeSheetByUserId.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(switchProject.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(switchProject.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(switchProject.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchAssignedShiftDetails.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchAssignedShiftDetails.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.shiftDetails = payload.data;
			})
			.addCase(fetchAssignedShiftDetails.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

export const {} = attendanceSlice.actions;
export default attendanceSlice.reducer;
