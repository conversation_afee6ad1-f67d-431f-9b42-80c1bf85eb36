'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';

import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';

import { Separator } from '@/components/ui/separator';
import {
	CheckCircle,
	Clock,
	XCircle,
	FileText,
	Filter,
	CreditCard,
	CalendarDays,
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { workParametersSchema } from '@/lib/schemas/companySchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { updateWorkParameters } from '@/lib/features/company-details/companyDetailsSlice';
import { useForm } from 'react-hook-form';
import { Input } from '@/components/ui/input';

const VerifyUpdates = () => {
	const dispatch = useAppDispatch();
	const { isLoading, companyData } = useAppSelector(
		(store) => store.companyDetails
	);

	const form = useForm({
		resolver: zodResolver(workParametersSchema),
		mode: 'onChange',

		defaultValues: {
			monthlySchedule: {
				total: 0,
				description: '',
			},
			dailySchedule: {
				total: 0,
				description: '',
			},
			hourlySchedule: {
				total: 0,
				description: '',
			},
		},
	});

	useEffect(() => {
		if (companyData) {
			form.setValue('monthlySchedule', {
				total: Number(companyData.companyDetails.monthlySchedule.total),
				description: companyData.companyDetails.monthlySchedule.description,
			});
			form.setValue('dailySchedule', {
				total: Number(companyData.companyDetails.dailySchedule.total),
				description: companyData.companyDetails.dailySchedule.description,
			});
			form.setValue('hourlySchedule', {
				total: Number(companyData.companyDetails.hourlySchedule.total),
				description: companyData.companyDetails.hourlySchedule.description,
			});
		}
	}, [companyData, form]);

	async function onSubmit(data) {
		dispatch(updateWorkParameters(data));
	}

	return (
		<section className="w-full h-full p-4">
			<header className="flex flex-col gap-2">
				<h1 className="text-xl md:text-2xl font-semibold">Work Parameters</h1>
				<p className="font-medium text-gray-500">
					Manage your companies work parameters.
				</p>
				<Separator />
			</header>

			{/* Main Content */}
			<main className="grid flex-1 items-start gap-4 md:gap-8 w-full mt-6">
				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)}>
						<div className="space-y-6">
							{['monthly', 'daily', 'hourly'].map((type, index) => {
								const icons = [CreditCard, CalendarDays, Clock];
								const labels = {
									monthly: 'Monthly Schedule',
									daily: 'Daily Schedule',
									hourly: 'Hourly Schedule',
								};
								const totalLabels = {
									monthly: 'Total Months',
									daily: 'Working Days',
									hourly: 'Working Hours',
								};
								const descriptions = {
									monthly: 'Number of months in a year',
									daily: 'Number of working days in a month',
									hourly: 'Number of working hours in a day',
								};
								const Icon = icons[index];
								return (
									<div key={type} className="">
										<div className="flex items-center gap-2">
											<Icon className="h-5 w-5 text-muted-foreground" />
											<h3 className="text-lg font-medium">{labels[type]}</h3>
										</div>
										<div className="grid gap-4 sm:grid-cols-2">
											<FormField
												control={form.control}
												name={`${type}Schedule.total`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>{totalLabels[type]}</FormLabel>
														<FormControl>
															<Input
																type="number"
																{...field}
																onChange={(e) =>
																	field.onChange(Number(e.target.value))
																}
																value={field.value}
															/>
														</FormControl>
														<FormDescription>
															{descriptions[type]}
														</FormDescription>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name={`${type}Schedule.description`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>Description</FormLabel>
														<FormControl>
															<Input {...field} />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>
									</div>
								);
							})}
						</div>
						<div className="flex justify-end mt-4">
							<Button type="submit" disabled={isLoading} className="ml-auto">
								{isLoading ? 'Saving...' : 'Save Changes'}
							</Button>
						</div>
					</form>
				</Form>
			</main>
		</section>
	);
};

export default VerifyUpdates;
