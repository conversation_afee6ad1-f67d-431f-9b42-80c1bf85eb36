'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ListFilter, PlusCircle } from 'lucide-react';
import BusinessUnitTable from './(business-unit)/table';
import DepartmentTable from './(department)/table';
import DesignationTable from './(designation)/table';
import BusinessUnitAddEditDialog from './(business-unit)/add-edit-dialog';
import DepartmentAddEditDialog from './(department)/add-edit-dialog';
import DesignationAddEditDialog from './(designation)/add-edit-dialog';
import { useState } from 'react';

const Settings = () => {
	const [showAddEditDialog, setShowAddEditDialog] = useState(false);
	const [isAdd, setIsAdd] = useState(false);

	const tabsTriggers = [
		{ name: 'Business Units', value: 'business-units' },
		{ name: 'Departments', value: 'departments' },
		{ name: 'Designations', value: 'designations' },
	];

	const tabsContents = [
		{
			value: 'business-units',
			title: 'Business Units',
			desc: 'Manage your business units, assign departments and admins.',
			component: <BusinessUnitTable />,
			dialogComponent: BusinessUnitAddEditDialog,
			dialogTitle: 'Add Business Unit',
			dialogDesc: 'Add a new business unit',
		},
		{
			value: 'departments',
			title: 'Departments',
			desc: 'Manage your departments and assign admins.',
			component: <DepartmentTable />,
			dialogComponent: DepartmentAddEditDialog,
			dialogTitle: 'Add Department',
			dialogDesc: 'Add a new department',
		},
		{
			value: 'designations',
			title: 'Designations',
			desc: 'Manage your designations.',
			component: <DesignationTable />,
			dialogComponent: DesignationAddEditDialog,
			dialogTitle: 'Add Designation',
			dialogDesc: 'Add a new designation',
		},
	];

	return (
		<section className="w-full h-full p-4">
			<header className="flex flex-col gap-2">
				<h1 className="text-xl md:text-2xl font-semibold">Settings</h1>
				<p className="font-medium text-gray-500">
					Manage business units, departments and designations.
				</p>
				<Separator />
			</header>
			<main className="grid flex-1 items-start gap-4 md:gap-8 w-full mt-4">
				<Tabs defaultValue={tabsTriggers[0].value}>
					<div className="flex items-center">
						<TabsList>
							{tabsTriggers.map((trigger) => (
								<TabsTrigger
									key={trigger.value}
									value={trigger.value}
									className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
								>
									{trigger.name}
								</TabsTrigger>
							))}
						</TabsList>
					</div>

					{tabsContents.map((content) => (
						<TabsContent key={content.value} value={content.value}>
							<Card>
								<CardHeader className="flex flex-row justify-between">
									<div>
										<CardTitle>{content.title}</CardTitle>
										<CardDescription>{content.desc}</CardDescription>
									</div>
									{content.dialogComponent && (
										<>
											<Button
												className=""
												onClick={() => {
													setShowAddEditDialog(true);
													setIsAdd(true);
												}}
											>
												<PlusCircle className="h-5 w-5" />
												Add
											</Button>
											{showAddEditDialog && (
												<content.dialogComponent
													isAdd={isAdd}
													title={content.dialogTitle}
													desc={content.dialogDesc}
													showAddEditDialog={showAddEditDialog}
													setShowAddEditDialog={setShowAddEditDialog}
												/>
											)}
										</>
									)}
								</CardHeader>
								<CardContent>{content.component}</CardContent>
							</Card>
						</TabsContent>
					))}
				</Tabs>
			</main>
		</section>
	);
};

export default Settings;
