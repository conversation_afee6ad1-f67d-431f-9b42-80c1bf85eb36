const { z } = require('zod');

export const addUpdateEquipmentSchema = z
	.object({
		_id: z.string().optional(),
		userId: z.string().optional().nullable(),
		equipmentName: z.string(),
		brand: z.string(),
		model: z.string(),
		serialNumber: z.string(),
		assetTag: z.string(),
		issueDate: z.preprocess((val) => new Date(val), z.date()),
		returnDate: z.preprocess((val) => new Date(val), z.date()).optional(),
		issueReason: z
			.enum(['new-hire', 'replacement', 'repair', 'other'])
			.optional(),
		returnReason: z
			.enum(['damaged', 'end-of-contract', 'upgrade', 'other'])
			.optional(),
	})
	.superRefine((data, ctx) => {
		if (data.userId) {
			if (!data.issueDate) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'Issue Date must not be empty',
					path: ['issueDate'],
				});
			}
			if (!data.issueReason) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'Issue reason must not be empty',
					path: ['issueReason'],
				});
			}
		}
	});

export const deleteEquipmentSchema = z.object({
	equipmentIds: z.array(z.string()),
});

export const addUpdateBusinessUnitSchema = z.object({
	businessUnits: z
		.array(
			z.object({
				_id: z.string().optional(),
				name: z.string().nonempty('Business Unit name is required'),
				location: z.string().nonempty('Business Unit location is required'),
				admin: z.string().optional(),
			})
		)
		.min(1, 'At least one business unit is required'),
});

export const addUpdateDepartmentSchema = z.object({
	departments: z
		.array(
			z.object({
				_id: z.string().optional(),
				name: z.string().nonempty('Department name is required'),
				businessUnitId: z.string().nonempty('Business Unit ID is required'),
				admin: z.string().optional(),
			})
		)
		.min(1, 'At least one department is required'),
});

export const addUpdateDesignationSchema = z.object({
	designations: z
		.array(
			z.object({
				_id: z.string().optional(),
				name: z.string().nonempty('Designation name is required'),
				departmentId: z.string().nonempty('Department ID is required'),
			})
		)
		.min(1, 'At least one designation is required'),
});

export const addUpdateModuleAdminSchema = z.object({
	moduleAdmins: z
		.array(
			z.object({
				_id: z.string().optional(),
				employeeId: z.string().nonempty('Employee is required'),
				moduleAdminAccess: z
					.array(z.string())
					.min(1, 'At least one module is required')
					.max(5, 'Maximum 5 modules allowed'),
			})
		)
		.min(1, 'At least one module admin is required'),
});

export const workParametersSchema = z.object({
	monthlySchedule: z.object({
		total: z.number().min(1, 'Total must be at least 1'),
		description: z.string().min(1, 'Description is required'),
	}),
	dailySchedule: z.object({
		total: z.number().min(1, 'Total must be at least 1'),
		description: z.string().min(1, 'Description is required'),
	}),
	hourlySchedule: z.object({
		total: z.number().min(1, 'Total must be at least 1'),
		description: z.string().min(1, 'Description is required'),
	}),
});

export const addUpdateHolidayGroupSchema = z.object({
	_id: z.string().optional(),
	name: z.string().min(1, 'Holiday group name is required'),
	businessUnit: z.array(z.string()).default([]),
	department: z.array(z.string()).optional().default([]),
	designation: z.array(z.string()).optional().default([]),
	employee: z.array(z.string()).optional().default([]),
});

export const addUpdateHolidaySchema = z.object({
	_id: z.string().optional(),
	holidayGroupId: z.string().nonempty('Holiday Group ID is required'),
	title: z.string().min(1, 'Holiday title is required'),
	startDate: z.preprocess((val) => new Date(val), z.date()),
	endDate: z.preprocess((val) => new Date(val), z.date()),
	icon: z.string().min(1, 'Holiday icon is required'),
});

export const copyHolidayGroupSchema = z.object({
	name: z.string().nonempty().min(1, 'Holiday Group name is required'),
	businessUnit: z.array(z.string()).default([]),
	department: z.array(z.string()).optional().default([]),
	designation: z.array(z.string()).optional().default([]),
	employee: z.array(z.string()).optional().default([]),
	holidays: z.array(
		z.object({
			title: z.string().min(1, 'Holiday title is required'),
			startDate: z.preprocess((val) => new Date(val), z.date()),
			isMultiDay: z.boolean(),
			endDate: z.preprocess((val) => new Date(val), z.date()).optional(),
			icon: z.string().min(1, 'Holiday icon is required'),
		})
	),
});
