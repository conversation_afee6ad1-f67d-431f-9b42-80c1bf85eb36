'use client';

import { useState } from 'react';
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	<PERSON>alogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';

const ConfirmDialog = ({
	renderTrigger,
	title = 'Are you sure?',
	description = 'This action cannot be undone.',
	confirmText = 'Confirm',
	cancelText = 'Cancel',
	confirmTextClassName,
	onConfirm = () => {},
	dialogType = 'default', // 'clockIn', 'clockOut', 'switchProject', 'default'
	projects = [], // Array of projects for selection
	showProjectSelect = false,
	selectedProject = null,
	onProjectChange = () => {},
}) => {
	const [open, setOpen] = useState(false);
	const [currentSelectedProject, setCurrentSelectedProject] =
		useState(selectedProject);

	const handleConfirm = () => {
		if (dialogType === 'clockIn' && showProjectSelect) {
			onConfirm(currentSelectedProject);
		} else if (dialogType === 'switchProject') {
			onConfirm(currentSelectedProject);
		} else {
			onConfirm();
		}
		setOpen(false);
	};

	const handleProjectChange = (projectId) => {
		setCurrentSelectedProject(projectId);
		onProjectChange(projectId);
	};

	const isConfirmDisabled = () => {
		if (
			(dialogType === 'clockIn' || dialogType === 'switchProject') &&
			showProjectSelect
		) {
			return !currentSelectedProject;
		}
		return false;
	};

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>{renderTrigger}</DialogTrigger>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>{description}</DialogDescription>
				</DialogHeader>

				{showProjectSelect && projects.length > 0 && (
					<div className="py-4">
						<label className="text-sm font-medium mb-2 block">
							Select Project
						</label>
						<Select
							value={currentSelectedProject || ''}
							onValueChange={handleProjectChange}
						>
							<SelectTrigger>
								<SelectValue placeholder="Choose a project" />
							</SelectTrigger>
							<SelectContent>
								{projects.map((project) => (
									<SelectItem key={project._id} value={project._id}>
										{project.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
				)}

				<DialogFooter>
					<Button variant="outline" onClick={() => setOpen(false)}>
						{cancelText}
					</Button>
					<Button
						onClick={handleConfirm}
						className={confirmTextClassName}
						// disabled={isConfirmDisabled()}
					>
						{confirmText}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default ConfirmDialog;
