'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from '@/components/ui/command';
import {
	createModuleAdmin,
	updateModuleAdmin,
} from '@/lib/features/module-admin/moduleAdminSlice';
import { fetchAllEmployees } from '@/lib/features/employees/employeeSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { moduleNames } from '@/lib/schemas/moduleAdminSchema';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { Check, ChevronsUpDown, Loader2, XCircle, Plus, X } from 'lucide-react';
import React, { useEffect } from 'react';
import { z } from 'zod';
import { useForm, useFieldArray } from 'react-hook-form';

// Available modules from schema
const availableModules = moduleNames
	? Object.keys(moduleNames).map((key) => ({
			value: key,
			label: moduleNames[key],
		}))
	: [];

// Form schema for the dialog (matches form structure)
const formSchema = z.object({
	moduleAdmins: z
		.array(
			z.object({
				employeeId: z.string().min(1, 'Employee is required'),
				modules: z.array(z.string()).min(1, 'At least one module is required'),
			})
		)
		.min(1, 'At least one module admin is required'),
});

const ModuleAdminAddEditDialog = ({
	isAdd,
	title,
	desc,
	moduleAdmin,
	showAddEditDialog,
	setShowAddEditDialog,
}) => {
	const dispatch = useAppDispatch();
	const { employees } = useAppSelector((store) => store.employee);
	const { isLoading } = useAppSelector((store) => store.moduleAdmin);

	const form = useForm({
		resolver: zodResolver(formSchema),
		defaultValues: {
			moduleAdmins: isAdd
				? [{ employeeId: '', modules: [] }]
				: [
						{
							employeeId: moduleAdmin?._id || '',
							modules: moduleAdmin?.moduleAdminAccess || [],
						},
					],
		},
		mode: 'onChange',
		reValidateMode: 'onSubmit',
	});

	const { fields, append, remove } = useFieldArray({
		control: form.control,
		name: 'moduleAdmins',
	});

	useEffect(() => {
		dispatch(fetchAllEmployees());
	}, [dispatch]);

	useEffect(() => {
		if (!isAdd && moduleAdmin) {
			form.reset({
				moduleAdmins: [
					{
						employeeId: moduleAdmin._id,
						modules: moduleAdmin.moduleAdminAccess || [],
					},
				],
			});
		}
	}, [moduleAdmin, isAdd, form]);

	const onSubmit = async ({ moduleAdmins }) => {
		// Map the form data to match backend schema
		const payload = {
			moduleAdmins: moduleAdmins.map(({ employeeId, modules }) => ({
				employeeId,
				modules,
			})),
		};

		const result = isAdd
			? await dispatch(createModuleAdmin(payload))
			: await dispatch(
					updateModuleAdmin({
						employeeId: moduleAdmins[0].employeeId,
						modules: moduleAdmins[0].modules,
					})
				);

		if (
			createModuleAdmin.fulfilled.match(result) ||
			updateModuleAdmin.fulfilled.match(result)
		) {
			setShowAddEditDialog(false);
			form.reset();
		}
	};

	const addModuleAdmin = () => {
		if (fields.length < 5) {
			append({ employeeId: '', modules: [] });
		}
	};

	const getEmployeeName = (employeeId) => {
		const employee = employees.find((emp) => emp._id === employeeId);
		return employee ? employee.name : 'Select Employee';
	};

	const getSelectLabel = (selectedValues, options) => {
		if (!selectedValues?.length) return 'Select...';
		if (selectedValues.includes('all')) return 'All Selected';
		return `${selectedValues.length} items Selected`;
	};

	const handleSelectionChange = (field, value) => {
		const currentValue = form.getValues(field) || [];
		let newValue;

		if (value === 'all') {
			newValue = ['all'];
		} else if (currentValue.includes('all')) {
			newValue = [value];
		} else if (currentValue.includes(value)) {
			newValue = currentValue.filter((v) => v !== value);
		} else {
			newValue = [...currentValue, value];
		}

		form.setValue(field, newValue);
	};

	const renderSelectedBadges = (selectedValues, options, onRemove) => {
		if (!selectedValues?.length) return null;

		const itemsToShow = selectedValues.includes('all')
			? options.filter((opt) => opt.value !== 'all')
			: selectedValues
					.map((value) => options.find((opt) => opt.value === value))
					.filter(Boolean);

		return (
			<div className="flex flex-wrap gap-1 mt-2">
				{itemsToShow.map((item) => (
					<Badge
						key={item.value}
						variant="secondary"
						className="flex items-center gap-1"
					>
						{item.label}
						<button
							type="button"
							onClick={() => onRemove(item.value)}
							className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
						>
							<X className="h-3 w-3" />
							<span className="sr-only">Remove {item.label}</span>
						</button>
					</Badge>
				))}
			</div>
		);
	};

	const handleBadgeRemove = (field, value) => {
		const currentValue = form.getValues(field) || [];
		const newValue = currentValue.filter((v) => v !== value);
		form.setValue(field, newValue);
	};

	return (
		<Dialog open={showAddEditDialog} onOpenChange={setShowAddEditDialog}>
			<DialogContent className="max-h-[80vh] overflow-hidden max-w-2xl">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>{desc}</DialogDescription>
				</DialogHeader>
				<Form {...form}>
					<form
						id="module-admin-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="grid gap-4 max-h-[50vh] overflow-y-auto pt-2 pb-2 pr-2"
					>
						{fields.map((moduleAdminField, index) => (
							<div
								key={moduleAdminField.id}
								className="relative grid gap-4 border p-4 rounded-lg"
							>
								{isAdd && fields.length > 1 && (
									<button
										type="button"
										onClick={() => remove(index)}
										className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
									>
										<XCircle className="text-red-500" size={16} />
									</button>
								)}

								{/* Employee Selection */}
								<FormField
									control={form.control}
									name={`moduleAdmins.${index}.employeeId`}
									render={({ field }) => (
										<FormItem>
											<FormLabel>Employee *</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value}
												disabled={!isAdd}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select an employee">
															{field.value && (
																<div className="flex items-center gap-2">
																	{getEmployeeName(field.value)}
																</div>
															)}
														</SelectValue>
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<ScrollArea className="h-48">
														{employees.map((employee) => (
															<SelectItem
																key={employee._id}
																value={employee._id}
															>
																{employee.name}
															</SelectItem>
														))}
													</ScrollArea>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								{/* Module Selection */}
								<FormField
									control={form.control}
									name={`moduleAdmins.${index}.modules`}
									render={({ field }) => (
										<FormItem className="flex flex-col">
											<FormLabel>Select Modules</FormLabel>
											<Popover>
												<PopoverTrigger asChild>
													<FormControl>
														<Button
															variant="outline"
															role="combobox"
															className={cn(
																'w-full justify-between',
																!field.value?.length && 'text-muted-foreground'
															)}
														>
															{getSelectLabel(field.value)}
															<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
														</Button>
													</FormControl>
												</PopoverTrigger>
												<PopoverContent className="w-full p-0" align="start">
													<Command>
														<CommandInput placeholder="Search modules..." />
														<CommandList>
															<CommandEmpty>No modules found.</CommandEmpty>
															<CommandGroup>
																{availableModules.map((option) => (
																	<CommandItem
																		key={option.value}
																		value={option.label}
																		onSelect={() => {
																			handleSelectionChange(
																				`moduleAdmins.${index}.modules`,
																				option.value
																			);
																		}}
																	>
																		<Check
																			className={cn(
																				'mr-2 h-4 w-4',
																				field.value?.includes(option.value)
																					? 'opacity-100'
																					: 'opacity-0'
																			)}
																		/>
																		{option.label}
																	</CommandItem>
																))}
															</CommandGroup>
														</CommandList>
													</Command>
												</PopoverContent>
											</Popover>
											{renderSelectedBadges(
												field.value,
												availableModules,
												(value) => {
													handleBadgeRemove(
														`moduleAdmins.${index}.modules`,
														value
													);
												}
											)}
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						))}

						{/* Add More Button - Only for Add Mode */}
						{isAdd && fields.length < 5 && (
							<Button
								type="button"
								variant="outline"
								onClick={addModuleAdmin}
								className="w-full"
							>
								<Plus className="mr-2 h-4 w-4" />
								Add Another Module Admin (Max 5)
							</Button>
						)}
					</form>
				</Form>
				<DialogFooter>
					<Button
						variant="outline"
						onClick={() => setShowAddEditDialog(false)}
						disabled={isLoading}
					>
						Cancel
					</Button>
					<Button type="submit" form="module-admin-form" disabled={isLoading}>
						{isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
						{isAdd ? 'Add Module Admin' : 'Update Module Admin'}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default ModuleAdminAddEditDialog;
