'use client';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import {
	ArrowUpDown,
	MapPin,
	Network,
	CheckCircle,
	XCircle,
	UserCheck,
} from 'lucide-react';
import { DataTableRowActions } from './row-actions';
import { Badge } from '@/components/ui/badge';
import { dummyEmployees } from '@/data/employeesData';

export const createColumns = (dispatch, isEditing, setIsEditing) => {
	return [
		{
			id: 'select',
			header: ({ table }) => (
				<Checkbox
					checked={table.getIsAllPageRowsSelected()}
					onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
					aria-label="Select all"
				/>
			),
			cell: ({ row }) => {
				return (
					<Checkbox
						checked={row.getIsSelected()}
						onCheckedChange={(value) => row.toggleSelected(!!value)}
						aria-label="Select row"
					/>
				);
			},
			enableSorting: false,
			enableHiding: false,
		},
		{
			accessorKey: '_id',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Department ID
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="font-medium">
					{row.original._id.slice(row.original._id.length - 6)}
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'name',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Name
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Network className="size-4 text-muted-foreground" />
					<span className="font-medium">{row.original.name}</span>
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'businessUnit.name',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Business Unit Name
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Network className="size-4 text-muted-foreground" />
					<span className="font-medium">
						{row.original.businessUnit.name || 'N/A'}
					</span>
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'businessUnit.location',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Business Unit Location
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<MapPin className="size-4 text-muted-foreground" />
					<span>{row.original.businessUnit.location || 'N/A'}</span>
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'admin',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Admin
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<UserCheck className="size-4 text-muted-foreground" />
					<span>{row.original.admin?.name || 'N/A'}</span>
				</div>
			),
			enableSorting: true,
		},
		{
			id: 'actions',
			cell: ({ row }) => <DataTableRowActions row={row} dispatch={dispatch} />,
		},
	];
};
