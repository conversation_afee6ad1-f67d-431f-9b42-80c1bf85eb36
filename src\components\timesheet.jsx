'use client';

import React, { useMemo, useState, useCallback, useEffect } from 'react';
import {
	useReactTable,
	getCoreRowModel,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
} from '@tanstack/react-table';
import debounce from 'lodash.debounce';
import { ArrowUpDown, ChevronLeft, ChevronRight } from 'lucide-react';

import { Table } from '@/components/ui/table';
import { TableActions } from '@/components/table-actions';
import { TablePagination } from '@/components/table-pagination';
import { THeader } from '@/components/table-header';
import { TBody } from '@/components/table-body';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { fetchTimeSheet } from '@/lib/features/attendance/attendanceSlice';
import { Checkbox } from './ui/checkbox';
import { Button } from './ui/button';

export function TimesheetTable() {
	const [sorting, setSorting] = useState([]);
	const [daysCount, setDaysCount] = useState(0);
	// const [columnFilters, setColumnFilters] = useState([]);
	const [rowSelection, setRowSelection] = useState({});
	const [globalFilter, setGlobalFilter] = useState('');
	const { timeSheets, isLoading } = useAppSelector((store) => store.attendance);
	const today = new Date();

	const [currentMonth, setCurrentMonth] = useState(today.getMonth() + 1); // JS months are 0-based
	const [currentYear, setCurrentYear] = useState(today.getFullYear());

	const dispatch = useAppDispatch();

	useEffect(() => {
		dispatch(
			fetchTimeSheet({
				month: 6,
			})
		);
	}, [dispatch]);

	useEffect(() => {
		dispatch(fetchTimeSheet({ month: currentMonth, year: currentYear }));
	}, [dispatch, currentMonth, currentYear]);

	useEffect(() => {
		if (timeSheets.length > 0) {
			const dayCount = timeSheets[0].presencePerDay.length;
			setDaysCount(dayCount);
		}
	}, [timeSheets]);

	const generateDays = (length) =>
		Array.from({ length }, (_, i) => String(i + 1).padStart(2, '0'));

	// Process the incoming data
	const processedData = useMemo(() => {
		if (!daysCount) return [];

		const days = generateDays(daysCount);

		return timeSheets.map((person) => {
			const row = { name: person.name };
			person.presencePerDay.forEach(({ date, present }) => {
				const day = date.slice(-2);
				row[day] = present;
			});
			return row;
		});
	}, [timeSheets, daysCount]); // ✅ Add daysCount as dependency

	const createTimesheetColumns = (daysCount) => {
		if (!daysCount) return [];

		const generateDays = (length) =>
			Array.from({ length }, (_, i) => String(i + 1).padStart(2, '0'));

		const dayColumns = generateDays(daysCount).map((day) => ({
			accessorKey: day,
			header: day,
			cell: ({ getValue }) => (
				<div className="min-w-[3rem]">{getValue() ? '✔️' : '❌'}</div>
			),
		}));

		return [
			{
				id: 'select',
				header: ({ table }) => (
					<Checkbox
						checked={table.getIsAllPageRowsSelected()}
						onCheckedChange={(value) =>
							table.toggleAllPageRowsSelected(!!value)
						}
						aria-label="Select all"
					/>
				),
				cell: ({ row }) => (
					<Checkbox
						checked={row.getIsSelected()}
						onCheckedChange={(value) => row.toggleSelected(!!value)}
						aria-label="Select row"
					/>
				),
				enableSorting: false,
				enableHiding: false,
			},
			{
				accessorKey: 'name',
				header: ({ column }) => (
					<button
						className="p-0 hover:bg-transparent text-left"
						onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					>
						Name <ArrowUpDown className="ml-2 h-4 w-4 inline" />
					</button>
				),
				cell: ({ getValue }) => (
					<div className="font-medium min-w-[10rem]">{getValue()}</div>
				),
				enableSorting: true,
			},
			...dayColumns,
		];
	};

	const columns = useMemo(() => createTimesheetColumns(daysCount), [daysCount]);

	const table = useReactTable({
		data: processedData,
		columns,
		onSortingChange: setSorting,
		onRowSelectionChange: setRowSelection,
		onGlobalFilterChange: setGlobalFilter,
		getCoreRowModel: getCoreRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		getSortedRowModel: getSortedRowModel(),
		state: {
			sorting,
			rowSelection,
			globalFilter,
		},
	});

	const debouncedSetGlobalFilter = useMemo(
		() =>
			debounce((value) => {
				setGlobalFilter(value);
			}, 300),
		[]
	);

	const handleFilterChange = useCallback(
		(e) => debouncedSetGlobalFilter(e.target.value),
		[debouncedSetGlobalFilter]
	);

	const handleBulkAction = (action) => {
		const selectedRows = table.getFilteredSelectedRowModel().rows;
		const selectedNames = selectedRows.map((row) => row.original.name);
		// console.log(`Bulk action: ${action}`, selectedNames);
	};

	const getMonthLabel = (month, year) =>
		new Date(year, month - 1).toLocaleString('default', {
			month: 'long',
			year: 'numeric',
		});

	const prevMonth = currentMonth === 1 ? 12 : currentMonth - 1;
	const prevYear = currentMonth === 1 ? currentYear - 1 : currentYear;

	const nextMonth = currentMonth === 12 ? 1 : currentMonth + 1;
	const nextYear = currentMonth === 12 ? currentYear + 1 : currentYear;

	const previousMonthLabel = getMonthLabel(prevMonth, prevYear);
	const nextMonthLabel = getMonthLabel(nextMonth, nextYear);

	return (
		<div className="overflow-x-auto ">
			<div className="flex justify-between items-center mb-2">
				<div className="flex gap-2">
					<Button
						variant="outline"
						onClick={() => {
							setCurrentMonth((prev) => {
								if (prev === 1) {
									setCurrentYear((y) => y - 1);
									return 12;
								}
								return prev - 1;
							});
						}}
					>
						<ChevronLeft className="mr-2" />
						{previousMonthLabel}
					</Button>

					<Button
						variant="outline"
						onClick={() => {
							setCurrentMonth((prev) => {
								if (prev === 12) {
									setCurrentYear((y) => y + 1);
									return 1;
								}
								return prev + 1;
							});
						}}
					>
						{nextMonthLabel}
						<ChevronRight className="ml-2" />
					</Button>
				</div>

				<span className="text-sm text-muted-foreground">
					Showing:{' '}
					<strong>
						{new Date(currentYear, currentMonth - 1).toLocaleString('default', {
							month: 'long',
							year: 'numeric',
						})}
					</strong>
				</span>
			</div>

			<TableActions
				table={table}
				handleFilterChange={handleFilterChange}
				handleBulkAction={handleBulkAction}
				bulkActions={[
					{
						label: 'Mark all present',
						value: 'mark-present',
					},
				]}
			/>

			<div className="rounded-md border w-[93vw] lg:max-w-full">
				<Table>
					<THeader table={table} />
					<TBody table={table} isLoading={isLoading} />
				</Table>
			</div>

			<TablePagination table={table} />
		</div>
	);
}
