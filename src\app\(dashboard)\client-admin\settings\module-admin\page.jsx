'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { PlusCircle } from 'lucide-react';
import ModuleAdminTable from './(module-admin-table)/module-admin-table';
import ModuleAdminAddEditDialog from './add-edit-dialog';

export default function ModuleAdminPage() {
	const [showAddEditDialog, setShowAddEditDialog] = useState(false);

	return (
		<div className="container mx-auto py-10 px-2">
			<div className="flex items-center justify-between mb-4">
				<div>
					<h1 className="text-2xl font-bold text-gray-700 dark:text-gray-100">
						Module Admin
					</h1>
					<p className="text-sm text-muted-foreground mt-1">
						Manage module administrators and their access permissions
					</p>
				</div>
				<Button
					onClick={() => setShowAddEditDialog(true)}
					className="flex items-center gap-2"
				>
					<PlusCircle className="h-4 w-4" />
					Add Module Admin
				</Button>
			</div>
			<Separator className="my-4" />
			<ModuleAdminTable />

			{/* Add Module Admin Dialog */}
			{showAddEditDialog && (
				<ModuleAdminAddEditDialog
					isAdd={true}
					title="Add Module Admin"
					desc="Assign module administration access to employees"
					showAddEditDialog={showAddEditDialog}
					setShowAddEditDialog={setShowAddEditDialog}
				/>
			)}
		</div>
	);
}
