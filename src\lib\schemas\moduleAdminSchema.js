const { z } = require('zod');

const moduleNames = {
	hr: 'HR Management',
	leave: 'Leave/Time-Off',
	attendance: 'Attendance',
	expense_claim: 'Expense Management',
	payroll: 'Payroll Management',
	communication: 'Communications',
	performance_and_appraisals: 'Performance & Appraisal',
	// project_and_tasks: 'Project & Tasks',
};

const createModuleAdminSchema = z.object({
	moduleAdmins: z
		.array(
			z.object({
				employeeId: z
					.string()
					.nonempty('Employee ID is required')
					.regex(/^[a-f\d]{24}$/, 'Invalid ID'),
				modules: z
					.array(z.enum(Object.keys(moduleNames)))
					.min(1, 'At least one module is required'),
			})
		)
		.min(1, 'At least one module admin is required'),
});

const updateModuleAdminSchema = z.object({
	employeeId: z
		.string()
		.nonempty('Employee ID is required')
		.regex(/^[a-f\d]{24}$/, 'Invalid ID'),
	modules: z
		.array(z.enum(Object.keys(moduleNames)))
		.min(1, 'At least one module is required'),
});

const deleteModuleAdminSchema = z.object({
	moduleAdminIds: z
		.array(z.string())
		.min(1, 'At least one module admin is required'),
});

module.exports = {
	createModuleAdminSchema,
	updateModuleAdminSchema,
	deleteModuleAdminSchema,
	moduleNames,
};
