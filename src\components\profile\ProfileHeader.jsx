'use client';

import { useState, useRef, useEffect } from 'react';
import { Edit, CircleX } from 'lucide-react';
import { Button } from '../ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Badge } from '../ui/badge';
import { AspectRatio } from '../ui/aspect-ratio';
import Image from 'next/image';
import { Mail, Phone, Calendar } from 'lucide-react';
import { formatDate, capitalize } from './utils/profileUtils';
import {
	coverImages,
	storedCoverImages,
} from '../project/data/background-images';
import {
	updateEmployeeCoverImage,
	updateEmployeeProfileImage,
} from '@/lib/features/employees/employeeSlice';
import { useDispatch } from 'react-redux';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '../ui/dialog';
import { cn } from '@/lib/utils';

export function ProfileHeader({
	personalDetails,
	designation,
	email,
	nationality,
}) {
	const [coverImage, setCoverImage] = useState('');
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const dispatch = useDispatch();

	useEffect(() => {
		if (personalDetails?.coverImage) {
			setCoverImage(personalDetails.coverImage);
		}
	}, [personalDetails]);

	const coverImageFallback = 'https://picsum.photos/1200/600?random=18';
	const [isUploadingProfile, setIsUploadingProfile] = useState(false);
	const profileImageInputRef = useRef(null);
	const [profilePreview, setProfilePreview] = useState(null);

	const handleProfileImageChange = async (event) => {
		const file = event.target.files[0];
		if (!file) return;

		const previewURL = URL.createObjectURL(file);
		setProfilePreview(previewURL);
		setIsUploadingProfile(true);

		try {
			await dispatch(updateEmployeeProfileImage({ profilePhoto: file }));
		} catch (error) {
			console.error('Error uploading profile image:', error);
		} finally {
			setIsUploadingProfile(false);
		}
	};

	const handleCoverImageSelect = async (image) => {
		const selectedImageUrl = image ? image.url : null;
		setCoverImage(selectedImageUrl);
		setIsDialogOpen(false);

		try {
			await dispatch(
				updateEmployeeCoverImage({
					coverImage: selectedImageUrl,
				})
			);
		} catch (error) {
			console.error('Error updating cover image:', error);
		}
	};

	return (
		<div className="flex flex-col md:flex-row gap-6 mb-5">
			{/* Profile Info Section (right side) */}
			<div className="relative flex-1 rounded-xl overflow-hidden p-6 max-h-72 min-w-[300px]">
				<input
					type="file"
					ref={profileImageInputRef}
					onChange={handleProfileImageChange}
					accept="image/*"
					className="hidden"
				/>

				<div className="flex flex-col md:flex-row items-center gap-6 dark:text-white">
					{/* Avatar */}
					<div className="relative group">
						<Avatar className="h-32 w-32 border-4 border-white shadow-lg">
							<AvatarImage
								src={
									profilePreview ||
									personalDetails?.profilePhoto ||
									'/placeholder.svg'
								}
								alt={personalDetails?.name || 'User'}
								className="object-cover"
							/>
							<AvatarFallback className="text-3xl bg-indigo-700">
								{personalDetails?.name
									? personalDetails.name
											.split(' ')
											.map((n) => n[0])
											.join('')
									: 'U'}
							</AvatarFallback>
						</Avatar>

						{/* Avatar Edit */}
						<div
							onClick={() => profileImageInputRef.current?.click()}
							className="absolute inset-0 bg-black/40 hidden group-hover:flex items-center justify-center cursor-pointer rounded-full"
						>
							<Edit className="h-6 w-6 text-white" />
						</div>

						<Badge className="absolute -bottom-2 right-0 bg-green-500 border-2 border-white">
							Active
						</Badge>
					</div>

					{/* Info */}
					<div className="text-center md:text-left">
						<h1 className="text-3xl font-bold">
							{personalDetails?.name || 'User Name'}
						</h1>

						<div className="flex flex-wrap justify-center md:justify-start gap-2 mt-2">
							<Badge
								variant="outline"
								className="bg-black/20 dark:bg-black/20 dark:text-gray-300 border-white/40 dark:border-gray-700"
							>
								ID: {personalDetails?.employeeOrgId || 'N/A'}
							</Badge>
							<Badge
								variant="outline"
								className="bg-black/20 dark:bg-black/20 dark:text-gray-300 border-white/40 dark:border-gray-700"
							>
								{designation}
							</Badge>
							<Badge
								variant="outline"
								className="bg-black/20 dark:bg-black/20 dark:text-gray-300 border-white/40 dark:border-gray-700"
							>
								{capitalize(nationality)}
							</Badge>
						</div>

						<div className="mt-4 flex flex-wrap justify-center md:justify-start gap-4">
							<div className="flex items-center gap-1">
								<Mail className="h-4 w-4" />
								<span>{email || 'N/A'}</span>
							</div>
							<div className="flex items-center gap-1">
								<Phone className="h-4 w-4" />
								<span>
									{personalDetails?.countryDialCode || ''}{' '}
									{personalDetails?.mobile || 'N/A'}
								</span>
							</div>
							<div className="flex items-center gap-1">
								<Calendar className="h-4 w-4" />
								<span>
									Joined: {formatDate(personalDetails?.dateOfJoining)}
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>
			{/* Cover Image Section (left side) */}
			<div className="relative flex-1 rounded-xl overflow-hidden max-h-72 min-w-[300px]">
				<AspectRatio ratio={16 / 9} className="w-full max-h-72">
					<Image
						src={
							coverImage || personalDetails?.coverImage || coverImageFallback
						}
						alt={personalDetails?.name || 'Employee Name'}
						className="object-cover dark:grayscale-0 transition duration-300"
						fill
					/>
				</AspectRatio>

				<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
					<DialogTrigger asChild>
						<Button
							size="sm"
							variant="secondary"
							className="absolute top-4 right-4 z-40 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-white/40"
						>
							<Edit className="h-4 w-4 mr-2" />
							Select Cover
						</Button>
					</DialogTrigger>
					<DialogContent className="sm:max-w-[700px] max-h-[95vh] overflow-y-auto p-4">
						<DialogHeader className="mb-2">
							<DialogTitle>Select Cover Image</DialogTitle>
						</DialogHeader>
						<div className="overflow-hidden">
							<div className="grid grid-cols-3 gap-3 max-h-[600px] overflow-y-auto">
								<button
									type="button"
									onClick={() => handleCoverImageSelect(null)}
									className={cn(
										'relative aspect-video rounded-lg overflow-hidden border-2 transition-all bg-background',
										coverImage === null ? 'border-primary' : 'border-muted'
									)}
								>
									<CircleX className="h-12 w-12 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-gray-400" />
									<div className="absolute inset-0 bg-black/20 flex items-end p-2">
										<span className="text-white text-xs font-medium">None</span>
									</div>
								</button>
								{coverImages.map((image) => (
									<button
										key={image.id}
										type="button"
										onClick={() => handleCoverImageSelect(image)}
										className={cn(
											'relative aspect-video rounded-lg overflow-hidden border-2 transition-all',
											coverImage === image.url
												? 'border-primary'
												: 'border-muted'
										)}
									>
										<Image
											src={image.thumbnail}
											alt={image.name}
											className="w-full h-full object-cover grayscale"
											width={300}
											height={200}
											loading="lazy"
										/>
										<div className="absolute inset-0 bg-black/20 flex items-end p-2">
											<span className="text-white text-xs font-medium">
												{image.name}
											</span>
										</div>
									</button>
								))}
							</div>
						</div>
					</DialogContent>
				</Dialog>

				<div className="absolute inset-0 dark:bg-black/40 z-20"></div>
			</div>
		</div>
	);
}
