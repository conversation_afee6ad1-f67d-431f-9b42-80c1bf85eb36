import React, { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
	Briefcase,
	Building,
	Edit,
	Loader2,
	MapPin,
	Plus,
	Save,
	Trash2,
	X,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { formatDate } from '@/lib/utils';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateEmployeeDetailsExperience } from '@/lib/features/employees/updateEmployeeSlice';
import { z } from 'zod';

// Define the schema for experience details
const experienceSchema = z.object({
	companyName: z.string().min(1, 'Company name is required'),
	designation: z.string().min(1, 'Designation is required'),
	location: z.string().min(1, 'Location is required'),
	periodFrom: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date is required'),
	periodTo: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'End date is required'),
	reasonForLeaving: z.string().optional(),
	_id: z.string().optional(),
});

const experienceDetailsSchema = z.object({
	experience: z.array(experienceSchema),
	deletedExperience: z.array(z.string()).optional(),
});

const EditEmployeeExperienceDetailsForm = ({ employeeId, experience = [] }) => {
	const { isLoading } = useAppSelector((store) => store.employee);
	const dispatch = useAppDispatch();
	const [isEditing, setIsEditing] = useState(false);
	const [originalFormValues, setOriginalFormValues] = useState(null);

	const form = useForm({
		resolver: zodResolver(experienceDetailsSchema),
		mode: 'onChange',

		defaultValues: {
			experience: experience.map((exp) => ({
				...exp,
				periodFrom: exp.periodFrom ? exp.periodFrom.split('T')[0] : '',
				periodTo: exp.periodTo ? exp.periodTo.split('T')[0] : '',
				reasonForLeaving: exp.reasonForLeaving || '',
			})),
			deletedExperience: [],
		},
	});

	const {
		fields: experienceFields,
		append: appendExperience,
		remove: removeExperience,
	} = useFieldArray({
		control: form.control,
		name: 'experience',
	});

	const handleExperienceDelete = (index) => {
		const experienceToDelete = form.getValues(`experience.${index}`);

		// If the experience record has an _id, add it to deletedExperience array
		if (experienceToDelete._id) {
			const currentDeletedExperience =
				form.getValues('deletedExperience') || [];
			form.setValue('deletedExperience', [
				...currentDeletedExperience,
				experienceToDelete._id,
			]);
		}

		// Remove the experience from the form array
		removeExperience(index);
	};

	const onSubmit = async (data) => {
		// Format the data for API
		const formattedData = {
			experience: data.experience.map((exp) => ({
				...exp,
				periodFrom: new Date(exp.periodFrom).toISOString().split('T')[0],
				periodTo: new Date(exp.periodTo).toISOString().split('T')[0],
			})),
			deletedExperience: data.deletedExperience || [],
			employeeId,
		};

		// Call the API to update experience details
		const result = await dispatch(
			updateEmployeeDetailsExperience({
				employeeId,
				...formattedData,
			})
		);

		// Check if update was successful
		if (updateEmployeeDetailsExperience.fulfilled.match(result)) {
			setIsEditing(false);
		}
	};

	const addNewExperience = () => {
		appendExperience({
			companyName: '',
			designation: '',
			location: '',
			periodFrom: '',
			periodTo: '',
			reasonForLeaving: '',
		});
	};

	return (
		<>
			{/* Edit Controls */}
			<div className="flex justify-end mb-4">
				<div className="flex gap-2">
					{isEditing && (
						<Button
							className="bg-red-600 text-white"
							variant="outline"
							onClick={() => {
								// Reset form to original values
								if (originalFormValues) {
									// First, reset the form with the original values
									form.reset(originalFormValues);

									// Force a re-render to ensure the form is properly updated
									setTimeout(() => {
										form.trigger();
									}, 0);
								}
								setIsEditing(false);
							}}
							disabled={isLoading}
						>
							{<X className="h-4 w-4 mr-2" size={16} />}Cancel
						</Button>
					)}
					<Button
						variant="default"
						onClick={() => {
							if (isEditing) {
								form.handleSubmit(onSubmit)();
							} else {
								// Save original form values before entering edit mode
								const currentValues = form.getValues();

								// Create a deep copy to ensure we don't have reference issues
								const deepCopy = JSON.parse(JSON.stringify(currentValues));
								setOriginalFormValues(deepCopy);

								setIsEditing(true);
							}
						}}
						disabled={isLoading}
					>
						{isLoading ? (
							<Loader2 className="animate-spin mr-2" size={16} />
						) : isEditing ? (
							<Save className="h-4 w-4 mr-2" size={16} />
						) : (
							<Edit className="h-4 w-4 mr-2" size={16} />
						)}
						{isEditing ? 'Save' : 'Edit'}
					</Button>
				</div>
			</div>

			<Card>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle className="flex items-center gap-2">
						<Briefcase className="h-5 w-5" />
						Work Experience
					</CardTitle>
					{isEditing && (
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={addNewExperience}
						>
							<Plus className="h-4 w-4 mr-2" />
							Add Experience
						</Button>
					)}
				</CardHeader>
				<CardContent>
					{isEditing ? (
						<Form {...form}>
							{experienceFields.length > 0 ? (
								<div className="space-y-6">
									{experienceFields.map((field, index) => (
										<div
											key={field.id}
											className="space-y-4 p-4 border rounded-md relative"
										>
											<Button
												type="button"
												variant="ghost"
												size="icon"
												className="absolute top-2 right-2 h-6 w-6 text-destructive"
												onClick={() => handleExperienceDelete(index)}
											>
												<Trash2 className="h-4 w-4" />
											</Button>

											<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
												<FormField
													control={form.control}
													name={`experience.${index}.companyName`}
													render={({ field }) => (
														<FormItem>
															<FormLabel className="text-sm font-medium text-muted-foreground">
																Company Name
															</FormLabel>
															<FormControl>
																<Input
																	placeholder="Enter company name"
																	{...field}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												<FormField
													control={form.control}
													name={`experience.${index}.designation`}
													render={({ field }) => (
														<FormItem>
															<FormLabel className="text-sm font-medium text-muted-foreground">
																Designation
															</FormLabel>
															<FormControl>
																<Input
																	placeholder="Enter designation"
																	{...field}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												<FormField
													control={form.control}
													name={`experience.${index}.location`}
													render={({ field }) => (
														<FormItem>
															<FormLabel className="text-sm font-medium text-muted-foreground">
																Location
															</FormLabel>
															<FormControl>
																<Input
																	placeholder="Enter location"
																	{...field}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												<div className="grid grid-cols-2 gap-2">
													<FormField
														control={form.control}
														name={`experience.${index}.periodFrom`}
														render={({ field }) => (
															<FormItem>
																<FormLabel className="text-sm font-medium text-muted-foreground">
																	From
																</FormLabel>
																<FormControl>
																	<Input type="date" {...field} />
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>

													<FormField
														control={form.control}
														name={`experience.${index}.periodTo`}
														render={({ field }) => (
															<FormItem>
																<FormLabel className="text-sm font-medium text-muted-foreground">
																	To
																</FormLabel>
																<FormControl>
																	<Input type="date" {...field} />
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>
												</div>

												<FormField
													control={form.control}
													name={`experience.${index}.reasonForLeaving`}
													render={({ field }) => (
														<FormItem className="md:col-span-2">
															<FormLabel className="text-sm font-medium text-muted-foreground">
																Reason for Leaving
															</FormLabel>
															<FormControl>
																<Textarea
																	placeholder="Enter reason for leaving"
																	className="resize-none"
																	{...field}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
											</div>
										</div>
									))}
								</div>
							) : (
								<div className="flex flex-col items-center justify-center py-6 text-center">
									<Briefcase className="h-8 w-8 text-muted-foreground mb-2" />
									<p className="text-muted-foreground">
										No work experience added yet
									</p>
									<Button
										type="button"
										variant="outline"
										size="sm"
										className="mt-4"
										onClick={addNewExperience}
									>
										<Plus className="h-4 w-4 mr-2" />
										Add Experience
									</Button>
								</div>
							)}
						</Form>
					) : (
						<>
							{experience.length > 0 ? (
								<div className="space-y-6">
									{experience.map((exp, index) => (
										<div key={exp._id || index} className="mb-6 last:mb-0">
											<div className="flex flex-col md:flex-row justify-between items-start gap-2 mb-2">
												<div>
													<h4 className="font-medium">{exp.designation}</h4>
													<p className="text-muted-foreground">
														{exp.companyName}
													</p>
												</div>
												<Badge variant="outline" className="w-fit">
													{formatDate(exp.periodFrom)} -{' '}
													{formatDate(exp.periodTo)}
												</Badge>
											</div>
											<div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
												<div>
													<p className="text-sm text-muted-foreground">
														Location
													</p>
													<p className="capitalize">{exp.location}</p>
												</div>
												<div>
													<p className="text-sm text-muted-foreground">
														Reason for Leaving
													</p>
													<p>{exp.reasonForLeaving || 'N/A'}</p>
												</div>
											</div>
											{index < experience.length - 1 && (
												<Separator className="my-4" />
											)}
										</div>
									))}
								</div>
							) : (
								<div className="flex items-center justify-center p-6 text-muted-foreground">
									<p>No work experience details available</p>
								</div>
							)}
						</>
					)}
				</CardContent>
			</Card>
		</>
	);
};

export default EditEmployeeExperienceDetailsForm;
