'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogFooter,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	Calendar,
	Music,
	Snowflake,
	Sun,
	Trash,
	Plus,
	ChevronsUpDown,
	Check,
	X,
	Loader2,
	MousePointerClick,
	Pointer,
} from 'lucide-react';
import { useForm, useWatch, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { copyHolidayGroupSchema } from '@/lib/schemas/companySchema';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from '@/components/ui/command';
import { Switch } from '@/components/ui/switch';
import { copyHolidayGroup } from '@/lib/features/holiday/holidayGroupSlice';
import { fetchPopulatedBusinessUnits } from '@/lib/features/company-infrastructure/businessUnitSlice';
import {
	updateHoliday,
	fetchHolidaysByHolidayGroupId,
} from '@/lib/features/holiday/holidaySlice';
import { icons } from '@/data/icons';

const CopyDialog = ({ holidayGroup, showCopyDialog, setShowCopyDialog }) => {
	const dispatch = useAppDispatch();
	const { holidayGroupHolidays, isLoading: holidayGroupHolidaysLoading } =
		useAppSelector((store) => store.holiday);
	const { populatedBusinessUnits: businessUnits } = useAppSelector(
		(store) => store.businessUnit
	);
	const [holidays, setHolidays] = useState([]);

	const form = useForm({
		resolver: zodResolver(copyHolidayGroupSchema),
		defaultValues: {
			name: holidayGroup?.name ? `Copy of ${holidayGroup.name}` : '',
			businessUnit: [],
			department: [],
			designation: [],
			employee: [],
			holidays: [],
		},
		mode: 'onChange',
		reValidateMode: 'onSubmit',
	});

	const {
		fields: holidayFields,
		append: appendHoliday,
		remove: removeHoliday,
	} = useFieldArray({
		name: 'holidays',
		control: form.control,
	});

	// Fetch business units if not already loaded
	useEffect(() => {
		if (businessUnits.length === 0) {
			dispatch(fetchPopulatedBusinessUnits());
		}
	}, [businessUnits.length, dispatch]);

	// Load holiday group data when component mounts
	useEffect(() => {
		if (holidayGroup) {
			if (holidayGroup.holidays.length > 0) {
				form.setValue(
					'holidays',
					holidayGroup.holidays.map((holiday) => ({
						...holiday,
						isMultiDay: holiday.endDate ? true : false,
					}))
				);
			}
			if (holidayGroup.assignment) {
				// Set business unit first
				form.setValue(
					'businessUnit',
					holidayGroup.assignment.businessUnit || []
				);
				form.setValue('department', holidayGroup.assignment.department || []);
				form.setValue('designation', holidayGroup.assignment.designation || []);
				form.setValue('employee', holidayGroup.assignment.employee || []);
			}

			if (showCopyDialog) {
				dispatch(fetchHolidaysByHolidayGroupId(holidayGroup._id));
			}
		}
	}, [showCopyDialog, dispatch, holidayGroup, form]);

	// Initialize holidays from holidayGroupHolidays
	useEffect(() => {
		if (holidayGroupHolidays?.length > 0) {
			const formattedHolidays = holidayGroupHolidays.map((holiday) => {
				const startDate = new Date(holiday.startDate);
				const endDate = holiday.endDate ? new Date(holiday.endDate) : null;
				const nextDay = new Date(startDate);
				nextDay.setDate(nextDay.getDate() + 1);

				return {
					...holiday,
					startDate: startDate.toISOString().split('T')[0],
					endDate: endDate
						? endDate.toISOString().split('T')[0]
						: nextDay.toISOString().split('T')[0],
					isMultiDay: !!holiday.endDate,
				};
			});

			form.setValue('holidays', formattedHolidays);
		}
	}, [holidayGroupHolidays, form]);

	// Set up watchers for form fields
	const watchBusinessUnit = useWatch({
		control: form.control,
		name: 'businessUnit',
	});

	const watchDepartment = useWatch({
		control: form.control,
		name: 'department',
	});

	const watchDesignation = useWatch({
		control: form.control,
		name: 'designation',
	});

	// Reset hierarchy when higher level selections change
	useEffect(() => {
		if (watchBusinessUnit && watchBusinessUnit.length > 0) {
			// Only reset if the user actively changes the selection
			if (document.activeElement?.name === 'businessUnit') {
				form.setValue('department', []);
				form.setValue('designation', []);
				form.setValue('employee', []);
			}
		}
	}, [watchBusinessUnit, form]);

	useEffect(() => {
		if (watchDepartment && watchDepartment.length > 0) {
			// Only reset if the user actively changes the selection
			if (document.activeElement?.name === 'department') {
				form.setValue('designation', []);
				form.setValue('employee', []);
			}
		}
	}, [watchDepartment, form]);

	useEffect(() => {
		if (watchDesignation && watchDesignation.length > 0) {
			// Only reset if the user actively changes the selection
			if (document.activeElement?.name === 'designation') {
				form.setValue('employee', []);
			}
		}
	}, [watchDesignation, form]);

	// Helper functions for hierarchy management
	const getDepartmentsForBusinessUnits = (selectedBusinessUnits) => {
		if (!selectedBusinessUnits?.length) return [];

		if (selectedBusinessUnits.includes('all')) {
			return businessUnits.flatMap((bu) => bu.departments || []);
		}

		return businessUnits
			.filter((bu) => selectedBusinessUnits.includes(bu._id))
			.flatMap((bu) => bu.departments || []);
	};

	const getDesignationsForDepartments = (selectedDepartments) => {
		if (!selectedDepartments?.length) return [];

		const departments = getDepartmentsForBusinessUnits(
			form.getValues('businessUnit')
		);
		if (selectedDepartments.includes('all')) {
			return departments.flatMap((dept) => dept.designations || []);
		}

		return departments
			.filter((dept) => selectedDepartments.includes(dept._id))
			.flatMap((dept) => dept.designations || []);
	};

	const getEmployeesForDesignations = (selectedDesignations) => {
		if (!selectedDesignations?.length) return [];

		const designations = getDesignationsForDepartments(
			form.getValues('department')
		);
		if (selectedDesignations.includes('all')) {
			return designations.flatMap((desig) => desig.employees || []);
		}

		return designations
			.filter((desig) => selectedDesignations.includes(desig._id))
			.flatMap((desig) => desig.employees || []);
	};

	// Helper function to get options for dropdowns
	const getOptions = (type, selectedValues) => {
		switch (type) {
			case 'businessUnit':
				return [
					{ value: 'all', label: 'All Business Units' },
					...businessUnits
						.filter((bu) => bu._id)
						.map((bu) => ({
							value: bu._id,
							label: `${bu.name} - ${bu.location}`,
						})),
				];
			case 'department':
				const departments = getDepartmentsForBusinessUnits(selectedValues);
				return [
					{ value: 'all', label: 'All Departments' },
					...departments
						.filter((dept) => dept._id)
						.map((dept) => ({
							value: dept._id,
							label: dept.name,
						})),
				];
			case 'designation':
				const designations = getDesignationsForDepartments(selectedValues);
				return [
					{ value: 'all', label: 'All Designations' },
					...designations
						.filter((desig) => desig._id)
						.map((desig) => ({
							value: desig._id,
							label: desig.name,
						})),
				];
			case 'employee':
				const employees = getEmployeesForDesignations(selectedValues);
				return [
					{ value: 'all', label: 'All Employees' },
					...employees
						.filter((emp) => emp.userId)
						.map((emp) => ({
							value: emp.userId,
							label: emp.userName,
						})),
				];
			default:
				return [];
		}
	};

	// Helper function to get label text for dropdowns
	const getSelectLabel = (selectedValues, options) => {
		if (!selectedValues?.length) return 'Select...';
		if (selectedValues.includes('all')) return 'All Selected';
		return `${selectedValues.length} items Selected`;
	};

	// Helper function to render selected badges
	const renderSelectedBadges = (selectedValues, options, onRemove) => {
		if (!selectedValues?.length) return null;

		const itemsToShow = selectedValues.includes('all')
			? options.filter((opt) => opt.value !== 'all')
			: selectedValues
					.map((value) => options.find((opt) => opt.value === value))
					.filter(Boolean);

		return (
			<div className="flex flex-wrap gap-1 mt-2">
				{itemsToShow.map((item) => (
					<Badge
						key={item.value}
						variant="secondary"
						className="flex items-center gap-1"
					>
						{item.label}
						<button
							type="button"
							onClick={() => onRemove(item.value)}
							className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
						>
							<X className="h-3 w-3" />
							<span className="sr-only">Remove {item.label}</span>
						</button>
					</Badge>
				))}
			</div>
		);
	};

	// Handle adding a new holiday
	const handleAddHoliday = () => {
		const today = new Date();
		const tomorrow = new Date(today);
		tomorrow.setDate(today.getDate() + 1);

		appendHoliday({
			title: '',
			startDate: today.toISOString().split('T')[0],
			endDate: tomorrow.toISOString().split('T')[0],
			icon: 'Calendar',
			isMultiDay: false,
		});
	};

	// Handle updating a holiday
	const handleUpdateHoliday = (index, field, value) => {
		const currentHolidays = form.getValues('holidays');
		const updatedHoliday = { ...currentHolidays[index] };

		if (field === 'startDate') {
			updatedHoliday.startDate = value;
			// Always set endDate to startDate + 1 when startDate changes
			const nextDay = new Date(value);
			nextDay.setDate(nextDay.getDate() + 1);
			updatedHoliday.endDate = nextDay.toISOString().split('T')[0];
		} else if (field === 'isMultiDay') {
			updatedHoliday.isMultiDay = value;
			if (!value) {
				// When switch is unchecked, set endDate to startDate + 1
				const nextDay = new Date(updatedHoliday.startDate);
				nextDay.setDate(nextDay.getDate() + 1);
				updatedHoliday.endDate = nextDay.toISOString().split('T')[0];
			}
		} else if (field === 'endDate') {
			// Only allow endDate to be changed if isMultiDay is true
			if (updatedHoliday.isMultiDay) {
				const selectedDate = new Date(value);
				const startDate = new Date(updatedHoliday.startDate);

				if (selectedDate < startDate) {
					// If selected date is before start date, set it to start date + 1
					const nextDay = new Date(startDate);
					nextDay.setDate(nextDay.getDate() + 1);
					updatedHoliday.endDate = nextDay.toISOString().split('T')[0];
				} else {
					updatedHoliday.endDate = value;
				}
			}
		} else {
			updatedHoliday[field] = value;
		}

		// Update the holiday in the form
		form.setValue(`holidays.${index}`, updatedHoliday);
	};

	// Handle deleting a holiday
	const handleDeleteHoliday = (index) => {
		removeHoliday(index);
	};

	// Handle hierarchy changes
	const handleBusinessUnitChange = (newValue) => {
		// Clear all dependent selections when business unit changes
		form.setValue('department', []);
		form.setValue('designation', []);
		form.setValue('employee', []);
	};

	const handleDepartmentChange = (newValue) => {
		// Clear designation and employee selections when department changes
		form.setValue('designation', []);
		form.setValue('employee', []);
	};

	const handleDesignationChange = (newValue) => {
		// Clear employee selections when designation changes
		form.setValue('employee', []);
	};

	// Handle selection changes
	const handleSelectionChange = (field, value) => {
		const currentValue = form.getValues(field) || [];
		let newValue;

		if (value === 'all') {
			newValue = ['all'];
		} else if (currentValue.includes('all')) {
			newValue = [value];
		} else if (currentValue.includes(value)) {
			newValue = currentValue.filter((v) => v !== value);
		} else {
			newValue = [...currentValue, value];
		}

		form.setValue(field, newValue);

		// Handle hierarchy changes
		switch (field) {
			case 'businessUnit':
				handleBusinessUnitChange(newValue);
				break;
			case 'department':
				handleDepartmentChange(newValue);
				break;
			case 'designation':
				handleDesignationChange(newValue);
				break;
		}
	};

	// Handle badge removal
	const handleBadgeRemove = (field, value) => {
		const currentValue = form.getValues(field) || [];
		const newValue = currentValue.filter((v) => v !== value);
		form.setValue(field, newValue);

		// Handle hierarchy changes
		switch (field) {
			case 'businessUnit':
				handleBusinessUnitChange(newValue);
				break;
			case 'department':
				handleDepartmentChange(newValue);
				break;
			case 'designation':
				handleDesignationChange(newValue);
				break;
		}
	};

	// Initialize form with existing values
	useEffect(() => {
		if (holidayGroup?.assignment) {
			form.setValue('businessUnit', holidayGroup.assignment.businessUnit || []);
			form.setValue('department', holidayGroup.assignment.department || []);
			form.setValue('designation', holidayGroup.assignment.designation || []);
			form.setValue('employee', holidayGroup.assignment.employee || []);
		}
	}, [holidayGroup, form]);

	// Submit handler
	const onSubmit = async (data) => {
		const assignment = {
			businessUnit: [],
			department: [],
			designation: [],
			employee: [],
		};
		let label = '';
		let valueIds = [];

		if (data.businessUnit && data.businessUnit.length > 0) {
			label = 'businessUnit';
			if (data.businessUnit.includes('all')) {
				valueIds = businessUnits.map((bu) => bu._id);
			} else {
				valueIds = data.businessUnit;
			}
			assignment[label] = valueIds;
		}
		if (data.department && data.department.length > 0) {
			label = 'department';
			if (data.department.includes('all')) {
				const departments = getDepartmentsForBusinessUnits(data.businessUnit);
				valueIds = departments.map((dept) => dept._id);
			} else {
				valueIds = data.department;
			}
			assignment[label] = valueIds;
		}
		if (data.designation && data.designation.length > 0) {
			label = 'designation';
			if (data.designation.includes('all')) {
				const designations = getDesignationsForDepartments(data.department);
				valueIds = designations.map((des) => des._id);
			} else {
				valueIds = data.designation;
			}
			assignment[label] = valueIds;
		}
		if (data.employee && data.employee.length > 0) {
			label = 'employee';
			if (data.employee.includes('all')) {
				const employees = getEmployeesForDesignations(data.designation);
				valueIds = employees.map((emp) => emp.userId);
			} else {
				valueIds = data.employee;
			}
			assignment[label] = valueIds;
		}

		const copyData = {
			name: data.name,
			assignment,
			assignTo: {
				label: label,
				value: valueIds,
			},
			holidays: data.holidays.map((holiday) => {
				const { title, startDate, endDate, isMultiDay, icon } = holiday;

				const filteredHoliday = {
					title,
					startDate,
					icon,
				};
				if (isMultiDay) {
					filteredHoliday.endDate = endDate;
				}
				return filteredHoliday;
			}),
		};

		const result = await dispatch(copyHolidayGroup(copyData));
		if (copyHolidayGroup.fulfilled.match(result)) {
			setShowCopyDialog(false);
		}
	};

	return (
		<Dialog open={showCopyDialog} onOpenChange={setShowCopyDialog}>
			<DialogContent className="max-w-4xl h-[90vh] gap-0 flex flex-col overflow-hidden p-0">
				<DialogHeader className="p-6">
					<DialogTitle className="text-xl">Copy Holiday Group</DialogTitle>
					<DialogDescription>
						Create a copy of &quot;{holidayGroup?.name}&quot; with editable
						details
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form
						id="holiday-group-copy-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="flex flex-col flex-1 overflow-hidden"
					>
						<ScrollArea className="flex-1 px-6">
							<div className="space-y-6">
								{/* Holiday Group Details Section */}
								<div>
									<FormField
										control={form.control}
										name="name"
										render={({ field }) => (
											<FormItem className="mb-6">
												<FormLabel>Name</FormLabel>
												<FormControl>
													<Input {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									{/* Selection Fields in a Grid Layout */}
									<div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
										{/* Business Unit Select */}
										<FormField
											control={form.control}
											name="businessUnit"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Business Unit</FormLabel>
													<Popover>
														<PopoverTrigger asChild>
															<FormControl>
																<Button
																	variant="outline"
																	role="combobox"
																	className={cn(
																		'w-full justify-between',
																		!field.value?.length &&
																			'text-muted-foreground'
																	)}
																>
																	{getSelectLabel(
																		field.value,
																		getOptions('businessUnit')
																	)}
																	<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
																</Button>
															</FormControl>
														</PopoverTrigger>
														<PopoverContent
															className="w-full p-0"
															align="start"
														>
															<Command>
																<CommandInput placeholder="Search business units..." />
																<CommandList>
																	<CommandEmpty>
																		No business unit found.
																	</CommandEmpty>
																	<CommandGroup>
																		{getOptions('businessUnit').map(
																			(option) => (
																				<CommandItem
																					key={option.value}
																					value={option.label}
																					onSelect={() => {
																						handleSelectionChange(
																							'businessUnit',
																							option.value
																						);
																					}}
																				>
																					<Check
																						className={cn(
																							'mr-2 h-4 w-4',
																							field.value?.includes(
																								option.value
																							)
																								? 'opacity-100'
																								: 'opacity-0'
																						)}
																					/>
																					{option.label}
																				</CommandItem>
																			)
																		)}
																	</CommandGroup>
																</CommandList>
															</Command>
														</PopoverContent>
													</Popover>
													{renderSelectedBadges(
														field.value,
														getOptions('businessUnit'),
														(value) => {
															handleBadgeRemove('businessUnit', value);
														}
													)}
													<FormMessage />
												</FormItem>
											)}
										/>

										{/* Department Select */}
										<FormField
											control={form.control}
											name="department"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Department (Optional)</FormLabel>
													<Popover>
														<PopoverTrigger asChild>
															<FormControl>
																<Button
																	variant="outline"
																	role="combobox"
																	className={cn(
																		'w-full justify-between',
																		!field.value?.length &&
																			'text-muted-foreground'
																	)}
																	disabled={!watchBusinessUnit?.length}
																>
																	{getSelectLabel(
																		field.value,
																		getOptions(
																			'department',
																			form.getValues('businessUnit')
																		)
																	)}
																	<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
																</Button>
															</FormControl>
														</PopoverTrigger>
														<PopoverContent
															className="w-full p-0"
															align="start"
														>
															<Command>
																<CommandInput placeholder="Search departments..." />
																<CommandList>
																	<CommandEmpty>
																		No department found.
																	</CommandEmpty>
																	<CommandGroup>
																		{getOptions(
																			'department',
																			form.getValues('businessUnit')
																		).map((option) => (
																			<CommandItem
																				key={option.value}
																				value={option.label}
																				onSelect={() => {
																					handleSelectionChange(
																						'department',
																						option.value
																					);
																				}}
																			>
																				<Check
																					className={cn(
																						'mr-2 h-4 w-4',
																						field.value?.includes(option.value)
																							? 'opacity-100'
																							: 'opacity-0'
																					)}
																				/>
																				{option.label}
																			</CommandItem>
																		))}
																	</CommandGroup>
																</CommandList>
															</Command>
														</PopoverContent>
													</Popover>
													{renderSelectedBadges(
														field.value,
														getOptions(
															'department',
															form.getValues('businessUnit')
														),
														(value) => {
															handleBadgeRemove('department', value);
														}
													)}
													<FormMessage />
												</FormItem>
											)}
										/>

										{/* Designation Select */}
										<FormField
											control={form.control}
											name="designation"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Designation (Optional)</FormLabel>
													<Popover>
														<PopoverTrigger asChild>
															<FormControl>
																<Button
																	variant="outline"
																	role="combobox"
																	className={cn(
																		'w-full justify-between',
																		!field.value?.length &&
																			'text-muted-foreground'
																	)}
																	disabled={!watchDepartment?.length}
																>
																	{getSelectLabel(
																		field.value,
																		getOptions(
																			'designation',
																			form.getValues('department')
																		)
																	)}
																	<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
																</Button>
															</FormControl>
														</PopoverTrigger>
														<PopoverContent
															className="w-full p-0"
															align="start"
														>
															<Command>
																<CommandInput placeholder="Search designations..." />
																<CommandList>
																	<CommandEmpty>
																		No designation found.
																	</CommandEmpty>
																	<CommandGroup>
																		{getOptions(
																			'designation',
																			form.getValues('department')
																		).map((option) => (
																			<CommandItem
																				key={option.value}
																				value={option.label}
																				onSelect={() => {
																					handleSelectionChange(
																						'designation',
																						option.value
																					);
																				}}
																			>
																				<Check
																					className={cn(
																						'mr-2 h-4 w-4',
																						field.value?.includes(option.value)
																							? 'opacity-100'
																							: 'opacity-0'
																					)}
																				/>
																				{option.label}
																			</CommandItem>
																		))}
																	</CommandGroup>
																</CommandList>
															</Command>
														</PopoverContent>
													</Popover>
													{renderSelectedBadges(
														field.value,
														getOptions(
															'designation',
															form.getValues('department')
														),
														(value) => {
															handleBadgeRemove('designation', value);
														}
													)}
													<FormMessage />
												</FormItem>
											)}
										/>

										{/* Employee Select */}
										<FormField
											control={form.control}
											name="employee"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Employee (Optional)</FormLabel>
													<Popover>
														<PopoverTrigger asChild>
															<FormControl>
																<Button
																	variant="outline"
																	role="combobox"
																	className={cn(
																		'w-full justify-between',
																		!field.value?.length &&
																			'text-muted-foreground'
																	)}
																	disabled={!watchDesignation?.length}
																>
																	{getSelectLabel(
																		field.value,
																		getOptions(
																			'employee',
																			form.getValues('designation')
																		)
																	)}
																	<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
																</Button>
															</FormControl>
														</PopoverTrigger>
														<PopoverContent
															className="w-full p-0"
															align="start"
														>
															<Command>
																<CommandInput placeholder="Search employees..." />
																<CommandList>
																	<CommandEmpty>
																		No employee found.
																	</CommandEmpty>
																	<CommandGroup>
																		{getOptions(
																			'employee',
																			form.getValues('designation')
																		).map((option) => (
																			<CommandItem
																				key={option.value}
																				value={option.label}
																				onSelect={() => {
																					handleSelectionChange(
																						'employee',
																						option.value
																					);
																				}}
																			>
																				<Check
																					className={cn(
																						'mr-2 h-4 w-4',
																						field.value?.includes(option.value)
																							? 'opacity-100'
																							: 'opacity-0'
																					)}
																				/>
																				{option.label}
																			</CommandItem>
																		))}
																	</CommandGroup>
																</CommandList>
															</Command>
														</PopoverContent>
													</Popover>
													{renderSelectedBadges(
														field.value,
														getOptions(
															'employee',
															form.getValues('designation')
														),
														(value) => {
															handleBadgeRemove('employee', value);
														}
													)}
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
								</div>

								{/* Holidays Section */}
								<div className="mt-6">
									<div className="mb-4">
										<h3 className="text-lg font-medium">Holidays</h3>
									</div>

									<div className="border rounded-md overflow-hidden">
										{holidayGroupHolidaysLoading ? (
											<div className="flex items-center justify-center p-4">
												<Loader2 className="h-6 w-6 animate-spin" />
											</div>
										) : (
											<Table>
												<TableHeader>
													<TableRow>
														<TableHead style={{ width: '40px' }}></TableHead>
														<TableHead>Title</TableHead>
														<TableHead>Start Date</TableHead>
														<TableHead>Multi Day</TableHead>
														<TableHead>End Date</TableHead>
														<TableHead>Actions</TableHead>
													</TableRow>
												</TableHeader>
												<TableBody>
													{holidayFields.length > 0 ? (
														holidayFields.map((field, index) => {
															const iconValue = form.watch(
																`holidays.${index}.icon`
															);
															const selectedIcon = icons.find(
																(i) => i.value === iconValue
															);

															const isMultiDayValue = form.watch(
																`holidays.${index}.isMultiDay`
															);
															const startDateValue = form.watch(
																`holidays.${index}.startDate`
															);

															return (
																<TableRow key={field.id}>
																	<TableCell>
																		<FormField
																			control={form.control}
																			name={`holidays.${index}.icon`}
																			render={({ field }) => (
																				<FormItem>
																					<Popover>
																						<PopoverTrigger asChild>
																							<FormControl>
																								<Button
																									variant="outline"
																									role="combobox"
																									className={cn(
																										'w-full justify-between',
																										!field.value &&
																											'text-muted-foreground'
																									)}
																								>
																									<div className="flex items-center">
																										{selectedIcon ? (
																											<selectedIcon.icon className="h-4 w-4" />
																										) : (
																											<Pointer className="h-4 w-4" />
																										)}
																									</div>
																								</Button>
																							</FormControl>
																						</PopoverTrigger>
																						<PopoverContent className="w-48 p-0">
																							<Command>
																								<CommandInput placeholder="Search icon..." />
																								<CommandList>
																									<CommandEmpty>
																										No icon found.
																									</CommandEmpty>
																									<CommandGroup>
																										{icons.map((icon) => (
																											<CommandItem
																												key={icon.value}
																												value={icon.value}
																												onSelect={() =>
																													field.onChange(
																														icon.value
																													)
																												}
																											>
																												<icon.icon className="mr-2 h-4 w-4" />
																												{icon.label}
																												<Check
																													className={cn(
																														'ml-auto h-4 w-4',
																														icon.value ===
																															field.value
																															? 'opacity-100'
																															: 'opacity-0'
																													)}
																												/>
																											</CommandItem>
																										))}
																									</CommandGroup>
																								</CommandList>
																							</Command>
																						</PopoverContent>
																					</Popover>
																					<FormMessage />
																				</FormItem>
																			)}
																		/>
																	</TableCell>
																	<TableCell>
																		<FormField
																			control={form.control}
																			name={`holidays.${index}.title`}
																			render={({ field }) => (
																				<FormItem>
																					<FormControl>
																						<Input {...field} className="h-8" />
																					</FormControl>
																					<FormMessage />
																				</FormItem>
																			)}
																		/>
																	</TableCell>
																	<TableCell>
																		<FormField
																			control={form.control}
																			name={`holidays.${index}.startDate`}
																			render={({ field }) => (
																				<FormItem>
																					<FormControl>
																						<Input
																							type="date"
																							{...field}
																							className="h-8"
																							min={
																								new Date()
																									.toISOString()
																									.split('T')[0]
																							}
																							onChange={(e) => {
																								field.onChange(e);
																								handleUpdateHoliday(
																									index,
																									'startDate',
																									e.target.value
																								);
																							}}
																						/>
																					</FormControl>
																					<FormMessage />
																				</FormItem>
																			)}
																		/>
																	</TableCell>
																	<TableCell className="text-center">
																		<FormField
																			control={form.control}
																			name={`holidays.${index}.isMultiDay`}
																			render={({ field }) => (
																				<FormItem>
																					<FormControl>
																						<Switch
																							checked={field.value}
																							onCheckedChange={(checked) => {
																								field.onChange(checked);
																								handleUpdateHoliday(
																									index,
																									'isMultiDay',
																									checked
																								);
																							}}
																						/>
																					</FormControl>
																					<FormMessage />
																				</FormItem>
																			)}
																		/>
																	</TableCell>
																	<TableCell>
																		<FormField
																			control={form.control}
																			name={`holidays.${index}.endDate`}
																			render={({ field }) => (
																				<FormItem>
																					<FormControl>
																						<Input
																							type="date"
																							{...field}
																							disabled={!isMultiDayValue}
																							min={startDateValue}
																							className={cn(
																								'h-8',
																								!isMultiDayValue &&
																									'opacity-50 cursor-not-allowed'
																							)}
																							onChange={(e) => {
																								if (isMultiDayValue) {
																									field.onChange(e);
																									handleUpdateHoliday(
																										index,
																										'endDate',
																										e.target.value
																									);
																								}
																							}}
																						/>
																					</FormControl>
																					<FormMessage />
																				</FormItem>
																			)}
																		/>
																	</TableCell>
																	<TableCell>
																		<Button
																			variant="ghost"
																			size="icon"
																			onClick={() => handleDeleteHoliday(index)}
																			className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
																		>
																			<Trash className="h-4 w-4" />
																			<span className="sr-only">
																				Delete holiday
																			</span>
																		</Button>
																	</TableCell>
																</TableRow>
															);
														})
													) : (
														<TableRow>
															<TableCell
																colSpan={7}
																className="text-center py-4"
															>
																No holidays in this group. Click &apos;Add
																Holiday&apos; to add one.
															</TableCell>
														</TableRow>
													)}
												</TableBody>
											</Table>
										)}

										<div className="p-2">
											<Button
												type="button"
												variant="outline"
												onClick={handleAddHoliday}
												className="w-full flex items-center justify-center gap-1"
											>
												<Plus className="h-4 w-4" />
												Add Holiday
											</Button>
										</div>
									</div>
								</div>
							</div>
						</ScrollArea>

						<DialogFooter className="p-6 mt-auto">
							<Button
								variant="outline"
								type="button"
								onClick={() => setShowCopyDialog(false)}
							>
								Cancel
							</Button>
							<Button type="submit">Create Copy</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
};

export default CopyDialog;
