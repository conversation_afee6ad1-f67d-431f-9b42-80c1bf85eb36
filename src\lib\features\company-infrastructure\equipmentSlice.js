import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { customFetch, showErrors } from '@/lib/utils';
import { toast } from 'sonner';

const initialState = {
	equipments: [],
	isLoading: false,
};

export const fetchEquipments = createAsyncThunk(
	'equipment/fetchEquipments',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch('/equipments');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateEquipment = createAsyncThunk(
	'equipment/updateEquipment',
	async (equipmentDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch('/equipments', equipmentDetails);

			if (data?.success) {
				thunkAPI.dispatch(fetchEquipments());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const addEquipment = createAsyncThunk(
	'equipment/addEquipment',
	async (equipmentDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post('/equipments', equipmentDetails);

			if (data?.success) {
				thunkAPI.dispatch(fetchEquipments());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const deleteEquipment = createAsyncThunk(
	'equipment/deleteEquipment',
	async (equipmentIds, thunkAPI) => {
		try {
			const { data } = await customFetch.patch('/equipments/remove', {
				equipmentIds,
			});

			if (data?.success) {
				thunkAPI.dispatch(fetchEquipments());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const equipmentSlice = createSlice({
	name: 'equipment',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchEquipments.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchEquipments.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.equipments = payload.data.equipments;
			})
			.addCase(fetchEquipments.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateEquipment.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Equipment...');
			})
			.addCase(updateEquipment.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateEquipment.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(addEquipment.pending, (state) => {
				state.isLoading = true;
				toast.info('Creating Equipment...');
			})
			.addCase(addEquipment.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(addEquipment.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(deleteEquipment.pending, (state) => {
				state.isLoading = true;
				toast.info('Deleting Equipment...');
			})
			.addCase(deleteEquipment.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(deleteEquipment.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

// export const {} = equipmentSlice.actions;
export default equipmentSlice.reducer;
