import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	addEquipment,
	updateEquipment,
} from '@/lib/features/company-infrastructure/equipmentSlice';
import { fetchAllEmployees } from '@/lib/features/employees/employeeSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { addUpdateEquipmentSchema } from '@/lib/schemas/companySchema';
import { cn, formatDate } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { CalendarIcon } from 'lucide-react';
import React, { useEffect } from 'react';
import { useForm, useWatch } from 'react-hook-form';

const EquipmentAddEditDialog = ({
	isAdd,
	title,
	desc,
	equipment,
	showAddEditDialog,
	setShowAddEditDialog,
}) => {
	const dispatch = useAppDispatch();
	const { employees } = useAppSelector((store) => store.employee);

	const form = useForm({
		resolver: zodResolver(addUpdateEquipmentSchema),
		defaultValues: {
			userId: 'none',
			equipmentName: '',
			brand: '',
			model: '',
			serialNumber: '',
			assetTag: '',
			issueDate: null,
			returnDate: null,
			issueReason: null,
			returnReason: null,
		},
		mode: 'onChange',
		reValidateMode: 'onSubmit',
	});

	// Watch userId to show/hide bottom section
	const selectedUserId = useWatch({
		control: form.control,
		name: 'userId',
	});

	// Watch dates for validation
	const issueDate = useWatch({
		control: form.control,
		name: 'issueDate',
	});

	const returnDate = useWatch({
		control: form.control,
		name: 'returnDate',
	});

	// Validate return date is after issue date
	useEffect(() => {
		if (issueDate && returnDate) {
			const issueDateObj = new Date(issueDate);
			const returnDateObj = new Date(returnDate);

			if (returnDateObj <= issueDateObj) {
				form.setError('returnDate', {
					type: 'manual',
					message: 'Return date must be after issue date',
				});
			} else {
				form.clearErrors('returnDate');
			}
		}
	}, [issueDate, returnDate, form]);

	useEffect(() => {
		if (!isAdd && equipment) {
			form.reset({
				userId: equipment.userId || 'none',
				equipmentName: equipment.equipmentName || '',
				brand: equipment.brand || '',
				model: equipment.model || '',
				serialNumber: equipment.serialNumber || '',
				assetTag: equipment.assetTag || '',
				issueDate: equipment.issueDate ? new Date(equipment.issueDate) : null,
				returnDate: equipment.returnDate
					? new Date(equipment.returnDate)
					: null,
				issueReason: equipment.issueReason || null,
				returnReason: equipment.returnReason || null,
			});
		}
	}, [isAdd, equipment, form]);

	useEffect(() => {
		if (showAddEditDialog && employees.length === 0) {
			dispatch(fetchAllEmployees());
		}
	}, [showAddEditDialog, employees.length, dispatch]);

	useEffect(() => {
		console.log(form.formState.errors);
	}, [form.formState.errors]);

	const onSubmit = async (formData) => {
		// Convert dates to ISO strings for API
		console.log(formData);
		const payload = {
			...formData,
			issueDate: formData.issueDate
				? formData.issueDate.toISOString().split('T')[0]
				: undefined,
			returnDate: formData.returnDate
				? formData.returnDate.toISOString().split('T')[0]
				: undefined,
		};
		if (payload.userId === 'none') {
			payload['userId'] = null;
			payload['issueDate'] = null;
			payload['issueReason'] = null;
			payload['returnDate'] = null;
			payload['returnReason'] = null;
		}

		const result = isAdd
			? await dispatch(addEquipment(payload))
			: await dispatch(updateEquipment({ _id: equipment._id, ...payload }));

		if (
			addEquipment.fulfilled.match(result) ||
			updateEquipment.fulfilled.match(result)
		) {
			setShowAddEditDialog(false);
		}
	};

	return (
		<Dialog open={showAddEditDialog} onOpenChange={setShowAddEditDialog}>
			<DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>{desc}</DialogDescription>
				</DialogHeader>
				<Form {...form}>
					<form
						id="equipment-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="space-y-8 max-h-[70vh] overflow-y-auto pt-2 pb-2 pr-2"
					>
						{/* Top Section - 2x3 Grid */}
						<div className="space-y-6">
							<div className="grid grid-cols-3 gap-6">
								<FormField
									control={form.control}
									name="equipmentName"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Equipment Name</FormLabel>
											<FormControl>
												<Input {...field} placeholder="Enter equipment name" />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="brand"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Brand</FormLabel>
											<FormControl>
												<Input {...field} placeholder="Enter brand" />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="model"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Model</FormLabel>
											<FormControl>
												<Input {...field} placeholder="Enter model" />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
							<div className="grid grid-cols-3 gap-6">
								<FormField
									control={form.control}
									name="serialNumber"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Serial Number</FormLabel>
											<FormControl>
												<Input {...field} placeholder="Enter serial number" />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="assetTag"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Asset Tag</FormLabel>
											<FormControl>
												<Input {...field} placeholder="Enter asset tag" />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="userId"
									render={({ field }) => (
										<FormItem>
											<FormLabel>User</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value || ''}
											>
												<SelectTrigger>
													<SelectValue placeholder="Select a user" />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="none">None</SelectItem>
													<ScrollArea className="max-h-60 overflow-y-auto">
														{employees.map((employee) => (
															<SelectItem
																key={employee._id}
																value={employee._id}
															>
																{employee.personalDetails?.name ||
																	employee.name}
															</SelectItem>
														))}
													</ScrollArea>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</div>

						{/* Bottom Section - 2x2 Grid (only visible when user is selected) */}
						{selectedUserId && selectedUserId !== 'none' && (
							<div className="space-y-6 border-t pt-6">
								<div className="grid grid-cols-2 gap-6">
									<FormField
										control={form.control}
										name="issueDate"
										render={({ field }) => (
											<FormItem className="flex flex-col">
												<FormLabel>Issue Date</FormLabel>
												<Popover>
													<PopoverTrigger asChild>
														<FormControl>
															<Button
																variant={'outline'}
																className={cn(
																	'w-full pl-3 text-left font-normal',
																	!field.value && 'text-muted-foreground'
																)}
															>
																{field.value ? (
																	formatDate(field.value)
																) : (
																	<span>Pick a date</span>
																)}
																<CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
															</Button>
														</FormControl>
													</PopoverTrigger>
													<PopoverContent className="w-auto p-0" align="start">
														<Calendar
															mode="single"
															selected={field.value}
															onSelect={field.onChange}
															initialFocus
														/>
													</PopoverContent>
												</Popover>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="issueReason"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Issue Reason</FormLabel>
												<Select
													onValueChange={field.onChange}
													value={field.value || ''}
												>
													<SelectTrigger>
														<SelectValue placeholder="Select issue reason" />
													</SelectTrigger>
													<SelectContent>
														<SelectItem value="new-hire">New Hire</SelectItem>
														<SelectItem value="replacement">
															Replacement
														</SelectItem>
														<SelectItem value="repair">Repair</SelectItem>
														<SelectItem value="other">Other</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
								<div className="grid grid-cols-2 gap-6">
									<FormField
										control={form.control}
										name="returnDate"
										render={({ field }) => (
											<FormItem className="flex flex-col">
												<FormLabel>Return Date</FormLabel>
												<Popover>
													<PopoverTrigger asChild>
														<FormControl>
															<Button
																variant={'outline'}
																className={cn(
																	'w-full pl-3 text-left font-normal',
																	!field.value && 'text-muted-foreground'
																)}
															>
																{field.value ? (
																	formatDate(field.value)
																) : (
																	<span>Pick a date</span>
																)}
																<CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
															</Button>
														</FormControl>
													</PopoverTrigger>
													<PopoverContent className="w-auto p-0" align="start">
														<Calendar
															mode="single"
															selected={field.value}
															onSelect={field.onChange}
															disabled={(date) => {
																// Disable dates before or equal to issue date
																if (issueDate) {
																	const issueDateObj = new Date(issueDate);
																	return date <= issueDateObj;
																}
																return false;
															}}
															initialFocus
														/>
													</PopoverContent>
												</Popover>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="returnReason"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Return Reason</FormLabel>
												<Select
													onValueChange={field.onChange}
													value={field.value || ''}
												>
													<SelectTrigger>
														<SelectValue placeholder="Select return reason" />
													</SelectTrigger>
													<SelectContent>
														<SelectItem value="damaged">Damaged</SelectItem>
														<SelectItem value="end-of-contract">
															End of Contract
														</SelectItem>
														<SelectItem value="upgrade">Upgrade</SelectItem>
														<SelectItem value="other">Other</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>
						)}
					</form>
				</Form>
				<DialogFooter>
					<Button variant="outline" onClick={() => setShowAddEditDialog(false)}>
						Cancel
					</Button>
					<Button type="submit" form="equipment-form">
						{isAdd ? 'Add Equipment' : 'Update Equipment'}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default EquipmentAddEditDialog;
