'use client';
import { motion } from 'motion/react';
import { Hero<PERSON>igh<PERSON>, Highlight } from './ui/hero-highlight';
import { DotBackground } from './ui/dot-background';
import Link from 'next/link';
// import { Button } from './ui/moving-border';
import logo from '@/assets/valluvalogo.png';
import Image from 'next/image';
import { Button } from './ui/button';
import { useCallback, useEffect } from 'react';
import axios from 'axios';

export function HeroHighlightSection() {
	const pingServer = useCallback(async () => {
		await axios.get('https://tms-backend-muzr.onrender.com/');
	}, []);

	useEffect(() => {
		pingServer();
	}, [pingServer]);

	return (
		<HeroHighlight>
			<div className="flex flex-col items-center justify-center gap-4">
				<motion.h1
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.5 }}
					whileHover={{
						scale: 1.03,
						textShadow: '0 0 8px rgba(255,255,255,0.8)',
					}}
					className="text-2xl px-4 md:text-4xl lg:text-5xl font-bold text-neutral-700 dark:text-white max-w-4xl leading-relaxed lg:leading-snug text-center mx-auto"
				>
					Productivity starts here at{' '}
					<motion.div
						initial={{ scale: 0, rotate: -10 }}
						animate={{ scale: 1, rotate: 0 }}
						transition={{
							duration: 0.6,
							delay: 0.3,
							type: 'spring',
							stiffness: 150,
						}}
						whileHover={{ scale: 1.1 }}
						className="flex items-center justify-center"
					>
						<Image
							src={logo}
							alt="logo"
							width={144}
							className="object-contain w-24 md:w-36 lg:w-40"
						/>
					</motion.div>
				</motion.h1>

				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.6 }}
					whileHover={{ scale: 1.05 }}
					whileTap={{ scale: 0.95 }}
				>
					<Link href="/login">
						<Button variant="outline">Get Started</Button>
					</Link>
				</motion.div>
			</div>
		</HeroHighlight>
	);
}
