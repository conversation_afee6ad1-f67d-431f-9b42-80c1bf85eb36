'use client';

import { useForm } from 'react-hook-form';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogDescription,
	DialogFooter,
} from '@/components/ui/dialog';
import {
	Form,
	FormItem,
	FormControl,
	FormField,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Button } from './ui/button';
import { cn } from '@/lib/utils';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { Check, ChevronsUpDown, X } from 'lucide-react';
import { Input } from './ui/input';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from './ui/command';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { useMemo, useState, useEffect } from 'react';
import { getChats, createChat } from '@/lib/features/chat/chatSlice';
import { Loader2 } from 'lucide-react';

const GroupChatDialog = ({ open, setOpen }) => {
	const dispatch = useAppDispatch();
	const [searchQuery, setSearchQuery] = useState('');

	const { chatUsers: employees, isLoading } = useAppSelector(
		(store) => store.chat
	);

	const form = useForm({
		defaultValues: {
			members: [],
			name: '',
		},
	});

	useEffect(() => {
		if (open) {
			form.reset();
		}
	}, [open]);

	const members = form.watch('members');

	const selectedEmployees = useMemo(() => {
		return employees.filter((emp) => members.includes(emp._id));
	}, [members, employees]);

	const onSubmit = async (data) => {
		const response = await dispatch(
			createChat({
				users: data.members,
				isGroup: true,
				name: data.name || 'New Group Chat',
			})
		);

		await dispatch(getChats());
		setOpen(false);
	};

	const removeMember = (id) => {
		form.setValue(
			'members',
			form.getValues('members').filter((mid) => mid !== id)
		);
	};

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogContent className="max-h-[80vh] overflow-hidden">
				<DialogHeader>
					<DialogTitle>Create Group Chat</DialogTitle>
					<DialogDescription>Select multiple employees</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form
						id="group-chat-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="grid gap-4"
					>
						{/* Group Name */}
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Group Name (optional)</FormLabel>
									<FormControl>
										<Input placeholder="e.g. Dev Team Chat" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Member Select */}
						<FormField
							control={form.control}
							name="members"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Members</FormLabel>
									<Popover>
										<PopoverTrigger asChild>
											<FormControl>
												<Button
													variant="outline"
													role="combobox"
													className={cn('w-full justify-between')}
												>
													{field.value.length
														? `${field.value.length} selected`
														: 'Select employees'}
													<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
												</Button>
											</FormControl>
										</PopoverTrigger>
										<PopoverContent className="w-full p-0" align="start">
											<Command>
												<CommandInput
													placeholder="Search employees..."
													className="h-9"
												/>
												<CommandEmpty>No employees found.</CommandEmpty>
												<CommandList className="w-full">
													{employees.map((emp) => (
														<CommandItem
															key={emp._id}
															value={emp.name}
															onSelect={() => {
																const newSelection = field.value.includes(
																	emp._id
																)
																	? field.value.filter((id) => id !== emp._id)
																	: [...field.value, emp._id];
																field.onChange(newSelection);
															}}
														>
															<Check
																className={cn(
																	'mr-2 h-4 w-4',
																	field.value.includes(emp._id)
																		? 'opacity-100'
																		: 'opacity-0'
																)}
															/>
															{emp.name}
														</CommandItem>
													))}
												</CommandList>
											</Command>
										</PopoverContent>
									</Popover>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Selected Members List */}
						{selectedEmployees.length > 0 && (
							<div className="flex flex-wrap gap-2 mt-2">
								{selectedEmployees.map((emp) => (
									<div
										key={emp._id}
										className="flex items-center gap-2 border px-2 py-1 rounded-md bg-muted"
									>
										<Avatar className="h-6 w-6">
											<AvatarImage
												src={emp.profilePhoto || '/placeholder.svg'}
												alt={emp.name}
											/>
											<AvatarFallback className="bg-muted text-sm">
												{emp.name?.slice(0, 2).toUpperCase()}
											</AvatarFallback>
										</Avatar>
										<span className="text-sm">{emp.name}</span>
										<X
											className="w-4 h-4 cursor-pointer"
											onClick={() => removeMember(emp._id)}
										/>
									</div>
								))}
							</div>
						)}
					</form>
				</Form>

				<DialogFooter>
					<Button variant="outline" onClick={() => setOpen(false)}>
						Cancel
					</Button>
					<Button
						variant="default"
						type="submit"
						form="group-chat-form"
						disabled={!members.length || isLoading}
					>
						{isLoading ? <Loader2 className="animate-spin" /> : 'Create Group'}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default GroupChatDialog;
