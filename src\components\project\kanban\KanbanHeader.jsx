'use client';

import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Checkbox } from '@/components/ui/checkbox';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import {
	Users,
	Settings,
	Filter,
	MoreHorizontal,
	ArrowLeft,
	Calendar,
	BarChart3,
	Share2,
	Eye,
	EyeOff,
	ChevronDown,
	ChevronUp,
	Sparkles,
	ChartBarIncreasing,
	X,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { SimpleLoader } from '@/components/loading-component';

/**
 * KanbanHeader Component
 * Header for the kanban board with project info and controls
 */
export const KanbanHeader = ({
	onBackClick,
	onSettingsClick,
	onFilterClick,
	onMembersClick,
	onMenuClick,
	onShareClick,
	onCalendarClick,
	onAnalyticsClick,
	onGlassModeToggle,
	isGlassMode = false,
	filters = { status: [], priority: [], assignee: [] },
	onFiltersChange,
	className,
	...props
}) => {
	const [isCollapsed, setIsCollapsed] = useState(true);
	const [isFilterOpen, setIsFilterOpen] = useState(false);
	const { projectDetails: project } = useAppSelector((store) => store.projects);
	const { taskBoard, isLoading } = useAppSelector((store) => store.tasks);

	if (!project) return null;

	// console.log(project, taskBoard);

	// Calculate project statistics from kanban data
	const allTasks =
		taskBoard?.groups?.reduce(
			(tasks, group) => [...tasks, ...group.tasks],
			[]
		) || [];
	const totalCards = allTasks.length;
	const completedCards = allTasks.filter(
		(task) => task.status === 'completed'
	).length;
	// const totalCards = 3;
	// const completedCards = 1;
	const progressPercentage =
		totalCards > 0 ? Math.round((completedCards / totalCards) * 100) : 0;

	const getStatusColor = (status) => {
		const colors = {
			active: 'bg-green-100 text-green-800',
			planning: 'bg-blue-100 text-blue-800',
			completed: 'bg-gray-100 text-gray-800',
			on_hold: 'bg-yellow-100 text-yellow-800',
		};
		return colors[status] || colors.active;
	};

	const getPriorityColor = (priority) => {
		const colors = {
			high: 'bg-red-100 text-red-800',
			medium: 'bg-yellow-100 text-yellow-800',
			low: 'bg-green-100 text-green-800',
		};
		return colors[priority] || colors.medium;
	};

	// Filter options
	const statusOptions = [
		{
			value: 'pending',
			label: 'Pending',
			color: 'bg-slate-100 text-slate-800',
		},
		{
			value: 'in-progress',
			label: 'In Progress',
			color: 'bg-blue-100 text-blue-800',
		},
		{
			value: 'completed',
			label: 'Completed',
			color: 'bg-green-100 text-green-800',
		},
		{
			value: 'cancelled',
			label: 'Cancelled',
			color: 'bg-red-100 text-red-800',
		},
		{
			value: 'overdue',
			label: 'Overdue',
			color: 'bg-orange-100 text-orange-800',
		},
	];

	const priorityOptions = [
		{ value: 'high', label: 'High', color: 'bg-red-100 text-red-800' },
		{
			value: 'medium',
			label: 'Medium',
			color: 'bg-yellow-100 text-yellow-800',
		},
		{ value: 'low', label: 'Low', color: 'bg-green-100 text-green-800' },
	];

	// Handle filter changes
	const handleFilterChange = (filterType, value, checked) => {
		if (!onFiltersChange) return;

		const newFilters = { ...filters };
		if (checked) {
			newFilters[filterType] = [...newFilters[filterType], value];
		} else {
			newFilters[filterType] = newFilters[filterType].filter(
				(item) => item !== value
			);
		}
		onFiltersChange(newFilters);
	};

	const clearAllFilters = () => {
		if (!onFiltersChange) return;
		onFiltersChange({ status: [], priority: [], assignee: [] });
	};

	const hasActiveFilters =
		filters.status.length > 0 ||
		filters.priority.length > 0 ||
		filters.assignee.length > 0;

	// if (isLoading) {
	// 	return (
	// 		<div className="min-h-screen items-center justify-center">
	// 			<SimpleLoader />
	// 		</div>
	// 	);
	// }
	return (
		<div
			className={cn(
				'bg-white/10 backdrop-blur-md border border-white/20',
				'p-4',
				'shadow-sm',
				'transition-all duration-300',
				className
			)}
			{...props}
		>
			<div className="flex items-center justify-between">
				{/* Left Section - Back button and Project Info */}
				<div className="flex items-center gap-4">
					<Button
						variant="ghost"
						size="sm"
						onClick={onBackClick}
						className={cn(
							'text-white/70 hover:text-white hover:bg-white/20',
							'p-2 transition-colors duration-300'
						)}
					>
						<ArrowLeft className="h-4 w-4" />
					</Button>

					<div className="flex items-center gap-3">
						<h1
							className={cn(
								'text-xl font-bold transition-colors duration-300',
								'text-white'
							)}
						>
							{project?.details?.name?.charAt(0).toUpperCase() +
								project?.details?.name?.slice(1)}
						</h1>

						<Badge
							className={cn(
								'capitalize text-xs',
								getStatusColor(project?.details?.status)
							)}
						>
							{project?.details?.status.replace('_', ' ')}
						</Badge>
					</div>
				</div>

				{/* Right Section - Controls and Members */}
				<div className="flex items-center gap-3">
					{/* Project Members */}
					{project?.assignedEmployees?.length > 0 && (
						<div className="flex items-center gap-2">
							<div className="flex -space-x-1">
								{project?.assignedEmployees.slice(0, 4).map((member, index) => (
									<Avatar
										key={member?.userId}
										src={member?.profilePhoto}
										className={cn(
											'w-8 h-8 border-2 ring-1 transition-colors duration-300',
											'border-white ring-white/20'
										)}
										style={{
											zIndex: project?.assignedEmployees.length - index,
										}}
									>
										<AvatarImage
											src={member.profilePhoto || '/placeholder.svg'}
											alt={member.name || 'Profile'}
										/>
										<AvatarFallback
											className={cn('text-xs bg-white/20', 'text-white')}
										>
											{member.name
												.split(' ')
												.map((n) => n[0])
												.join('')}
										</AvatarFallback>
									</Avatar>
								))}

								{project?.assignedEmployees?.length > 4 && (
									<div
										className={cn(
											'w-8 h-8 rounded-full bg-white/20 border-2 ring-1 flex items-center justify-center text-xs font-medium',
											'border-white ring-white/20 text-white'
										)}
									>
										+{project?.assignedEmployees.length - 4}
									</div>
								)}
							</div>
						</div>
					)}

					{/* Filter Button */}
					<Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
						<PopoverTrigger asChild>
							<Button
								variant="ghost"
								size="sm"
								className={cn(
									'text-white/70 hover:text-white hover:bg-white/20',
									'p-2 transition-colors duration-300 relative',
									hasActiveFilters && 'text-white bg-white/20'
								)}
								title="Filter tasks"
							>
								<Filter className="h-4 w-4" />
								{hasActiveFilters && (
									<div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-400 rounded-full" />
								)}
							</Button>
						</PopoverTrigger>
						<PopoverContent className="w-80 p-0" align="end" side="bottom">
							<div className="p-4">
								{/* Filter Header */}
								<div className="flex items-center justify-between mb-4">
									<h3 className="font-semibold text-sm">Filter Tasks</h3>
									<div className="flex items-center gap-2">
										{hasActiveFilters && (
											<Button
												variant="ghost"
												size="sm"
												onClick={clearAllFilters}
												className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
											>
												Clear All
											</Button>
										)}
										<Button
											variant="ghost"
											size="sm"
											onClick={() => setIsFilterOpen(false)}
											className="h-6 w-6 p-0"
										>
											<X className="h-3 w-3" />
										</Button>
									</div>
								</div>

								{/* Status Filter */}
								<div className="mb-4">
									<h4 className="text-xs font-medium text-muted-foreground mb-2 uppercase tracking-wide">
										Status
									</h4>
									<div className="space-y-2">
										{statusOptions.map((status) => (
											<div
												key={status.value}
												className="flex items-center space-x-2"
											>
												<Checkbox
													id={`status-${status.value}`}
													checked={filters.status.includes(status.value)}
													onCheckedChange={(checked) =>
														handleFilterChange('status', status.value, checked)
													}
												/>
												<label
													htmlFor={`status-${status.value}`}
													className="text-sm cursor-pointer flex items-center gap-2"
												>
													<Badge className={cn('text-xs', status.color)}>
														{status.label}
													</Badge>
												</label>
											</div>
										))}
									</div>
								</div>

								{/* Priority Filter */}
								<div className="mb-4">
									<h4 className="text-xs font-medium text-muted-foreground mb-2 uppercase tracking-wide">
										Priority
									</h4>
									<div className="space-y-2">
										{priorityOptions.map((priority) => (
											<div
												key={priority.value}
												className="flex items-center space-x-2"
											>
												<Checkbox
													id={`priority-${priority.value}`}
													checked={filters.priority.includes(priority.value)}
													onCheckedChange={(checked) =>
														handleFilterChange(
															'priority',
															priority.value,
															checked
														)
													}
												/>
												<label
													htmlFor={`priority-${priority.value}`}
													className="text-sm cursor-pointer flex items-center gap-2"
												>
													<Badge className={cn('text-xs', priority.color)}>
														{priority.label}
													</Badge>
												</label>
											</div>
										))}
									</div>
								</div>

								{/* Assignee Filter */}
								{project?.assignedEmployees?.length > 0 && (
									<div className="mb-4">
										<h4 className="text-xs font-medium text-muted-foreground mb-2 uppercase tracking-wide">
											Assignee
										</h4>
										<div className="space-y-2">
											{project.assignedEmployees.map((employee) => (
												<div
													key={employee.userId}
													className="flex items-center space-x-2"
												>
													<Checkbox
														id={`assignee-${employee.userId}`}
														checked={filters.assignee.includes(employee.userId)}
														onCheckedChange={(checked) =>
															handleFilterChange(
																'assignee',
																employee.userId,
																checked
															)
														}
													/>
													<label
														htmlFor={`assignee-${employee.userId}`}
														className="text-sm cursor-pointer flex items-center gap-2"
													>
														<Avatar className="w-5 h-5">
															<AvatarImage
																src={
																	employee.profilePhoto || '/placeholder.svg'
																}
																alt={employee.name || 'Profile'}
															/>
															<AvatarFallback className="text-xs">
																{employee.name
																	.split(' ')
																	.map((n) => n[0])
																	.join('')}
															</AvatarFallback>
														</Avatar>
														<span>{employee.name}</span>
													</label>
												</div>
											))}
										</div>
									</div>
								)}
							</div>
						</PopoverContent>
					</Popover>

					{/* Glass Mode Toggle Button */}
					{/* <Button
						variant="ghost"
						size="sm"
						onClick={onGlassModeToggle}
						className={cn(
							isGlassMode
								? 'text-white/70 hover:text-white hover:bg-white/20'
								: 'text-muted-foreground hover:text-foreground hover:bg-muted',
							'p-2 transition-colors duration-300'
						)}
						title={
							isGlassMode ? 'Switch to solid mode' : 'Switch to glass mode'
						}
					>
						<Sparkles
							className={cn(
								'h-4 w-4 transition-all duration-300',
								isGlassMode ? 'text-muted-foreground' : 'text-foreground'
							)}
						/>
					</Button> */}

					{/* Collapse/Expand Button */}
					<Button
						variant="ghost"
						size="sm"
						onClick={() => setIsCollapsed(!isCollapsed)}
						className={cn(
							'text-white/70 hover:text-white hover:bg-white/20',
							'p-2 transition-colors duration-300'
						)}
					>
						<ChartBarIncreasing className="h-4 w-4" />
					</Button>
				</div>
			</div>

			{/* Project Description and Progress */}
			{!isCollapsed && (
				<div
					className={cn(
						'mt-3 pt-3 transition-all duration-300',
						'border-t border-white/20'
					)}
				>
					{project?.details?.description && (
						<p
							className={cn(
								'text-sm max-w-2xl mb-3 transition-colors duration-300',
								'text-white/80'
							)}
						>
							{project.details.description}
						</p>
					)}

					{totalCards > 0 && (
						<div className="flex items-center gap-3">
							<div className="flex-1 max-w-xs">
								<div
									className={cn(
										'flex items-center justify-between text-xs mb-1 transition-colors duration-300',
										'text-white/80'
									)}
								>
									<span>Progress</span>
									<span>{progressPercentage}%</span>
								</div>
								<div
									className={cn(
										'w-full rounded-full h-2 transition-colors duration-300 dark:text-foreground',
										'bg-white/20'
									)}
								>
									<div
										className={cn(
											'h-2 rounded-full transition-all duration-300 dark:text-foreground',
											'bg-white/80'
										)}
										style={{ width: `${progressPercentage}%` }}
									/>
								</div>
							</div>
							<div
								className={cn(
									'text-xs transition-colors duration-300 dark:text-foreground',
									'text-white/70'
								)}
							>
								{completedCards} of {totalCards} tasks completed
							</div>
						</div>
					)}
				</div>
			)}
		</div>
	);
};
