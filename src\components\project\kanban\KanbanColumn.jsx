'use client';

import React, { useState, useEffect } from 'react';
import { Droppable } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuTrigger,
	DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from '@/components/ui/form';
import {
	Plus,
	Check,
	ChevronLeft,
	ChevronRight,
	XIcon,
	EllipsisVertical,
	Loader2,
} from 'lucide-react';
import { cn, generateRandomSixCharCode } from '@/lib/utils';
import { KanbanCard } from './KanbanCard';
import { TaskModal } from './TaskModal';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { createTask, updateTaskGroup } from '@/lib/features/tasks/tasksSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { useRouter, useSearchParams } from 'next/navigation';

// Task creation schema (simplified for quick task creation)
const createTaskSchema = z.object({
	name: z
		.string()
		.nonempty('Task name is required')
		.max(50, 'Task name must be at most 50 characters long'),
});

const updateTaskGroupSchema = z.object({
	name: z.string().nonempty('Task group name is required'),
});

/**
 * KanbanGroup Component
 * Represents a group in the kanban board (e.g., To Do, In Progress, Done)
 */
export const KanbanColumn = ({
	group,
	tasks = [],
	isDropTarget = false,
	isGlassMode = false,
	isDropdownOpen = false,
	onDropdownOpenChange,
	className,
	...props
}) => {
	const dispatch = useAppDispatch();
	const searchParams = useSearchParams();
	const router = useRouter();
	const [isAddingTask, setIsAddingTask] = useState(false);
	const [isEditing, setIsEditing] = useState(false);
	const [isCollapsed, setIsCollapsed] = useState(false);
	const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
	const [taskId, setTaskId] = useState(null);
	const { isLoading: isGroupUpdating } = useAppSelector((state) => state.tasks);

	// React Hook Form for group name update
	const groupForm = useForm({
		resolver: zodResolver(updateTaskGroupSchema),
		defaultValues: {
			name: group.name,
		},
	});

	// React Hook Form for quick task creation
	const taskForm = useForm({
		resolver: zodResolver(createTaskSchema),
		defaultValues: {
			name: '',
		},
	});

	// Form submission handler for task creation
	const onSubmitTask = async (data) => {
		const taskData = {
			...data,
			projectId: group.projectId,
			code: generateRandomSixCharCode(),
			groupId: group._id,
		};
		const result = await dispatch(createTask(taskData));

		if (createTask.fulfilled.match(result)) {
			setIsAddingTask(false);
			taskForm.reset();
		}
	};

	// Form submission handler for group name update
	const onSubmitGroup = async (data) => {
		if (data.name.trim() && data.name !== group.name) {
			const result = await dispatch(
				updateTaskGroup({
					projectId: group.projectId,
					groupId: group._id,
					companyId: group.companyId,
					name: data.name,
				})
			);
			if (updateTaskGroup.fulfilled.match(result)) {
				setIsEditing(false);
				groupForm.reset({ name: data.name });
			}
		} else {
			setIsEditing(false);
			groupForm.reset({ name: group.name });
		}
	};

	const handleTaskClick = (task) => {
		const current = new URLSearchParams(Array.from(searchParams.entries()));
		current.set('task', task._id);
		router.replace(`?${current.toString()}`, { scroll: false });

		setIsTaskModalOpen(true);
		setTaskId(task._id);
	};

	const handleTaskModalClose = () => {
		const current = new URLSearchParams(Array.from(searchParams.entries()));
		current.delete('task');
		router.replace(`?${current.toString()}`, { scroll: false });

		setIsTaskModalOpen(false);
		setTaskId(null);
	};

	const handleTaskSave = (updatedTask) => {
		// TODO: Update task in the group data
		// console.log('Task saved:', updatedTask);
		// In a real app, this would call an API or update state
	};

	const actions = [
		{
			value: 'edit',
			label: 'Edit Group Name',
			action: () => setIsEditing(true),
		},
	];

	return (
		<div
			className={cn(
				'flex-shrink-0 h-fit transition-all duration-300',
				isCollapsed ? 'w-12' : 'w-[18rem]',
				className
			)}
			{...props}
		>
			<Card
				className={cn(
					'shadow-sm transition-all duration-300 flex flex-col gap-2 bg-card text-card-foreground border border-border',
					isCollapsed ? 'p-1' : 'p-2'
				)}
				role="region"
				aria-label={`${group.name} group with ${tasks.length} tasks`}
			>
				<CardHeader className="transition-all duration-300 p-2">
					{isCollapsed ? (
						<div className="flex flex-col items-center gap-2 h-full">
							<Button
								variant="ghost"
								size="sm"
								onClick={() => setIsCollapsed(false)}
								className="h-6 w-6 p-0 transition-all duration-200 text-muted-foreground hover:text-foreground hover:bg-muted"
								title="Expand group"
							>
								<ChevronRight className="h-4 w-4" />
							</Button>
							<div className="flex flex-col items-center gap-2 flex-1">
								<div
									className="text-sm font-semibold writing-mode-vertical-rl text-orientation-mixed transition-colors duration-200z text-foreground"
									style={{
										writingMode: 'vertical-rl',
										textOrientation: 'mixed',
									}}
									title={group.name}
								>
									{group.name}
								</div>
								<Badge
									variant="secondary"
									className="text-xs px-1 py-0.5 min-w-[20px] text-center bg-muted text-muted-foreground"
								>
									{tasks.length}
								</Badge>
							</div>
						</div>
					) : (
						<div>
							{isEditing ? (
								<div className="flex items-center justify-between gap-2">
									<Form {...groupForm}>
										<form
											onSubmit={groupForm.handleSubmit(onSubmitGroup)}
											className="flex items-center gap-2 w-full"
										>
											<FormField
												control={groupForm.control}
												name="name"
												render={({ field }) => (
													<FormItem className="flex-1">
														<FormControl>
															<Input
																{...field}
																type="text"
																disabled={isGroupUpdating}
																className="h-8 text-sm bg-input border border-border text-foreground placeholder-muted-foreground"
																autoFocus
																onKeyDown={(e) => {
																	if (e.key === 'Enter') {
																		groupForm.handleSubmit(onSubmitGroup)();
																	} else if (e.key === 'Escape') {
																		setIsEditing(false);
																		groupForm.reset({ name: group.name });
																	}
																}}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<div>
												{isGroupUpdating ? (
													<div>
														<Loader2 className="h-4 w-4 animate-spin" />
													</div>
												) : (
													<div className="flex items-center gap-1">
														<Button
															type="submit"
															size="icon"
															disabled={!groupForm.watch('name')?.trim()}
														>
															<Check />
														</Button>
														<Button
															variant="destructive"
															size="icon"
															onClick={() => {
																setIsEditing(false);
																groupForm.reset({ name: group.name });
															}}
														>
															<XIcon />
														</Button>
													</div>
												)}
											</div>
										</form>
									</Form>
								</div>
							) : (
								<div className="flex items-center gap-2 justify-between w-full">
									<div className="flex items-center gap-2">
										<Button
											variant="ghost"
											size="sm"
											onClick={() => setIsCollapsed(true)}
											className={cn(
												'h-6 w-6 p-0 transition-all duration-200 text-muted-foreground hover:text-foreground hover:bg-muted'
											)}
											title="Collapse group"
										>
											<ChevronLeft className="h-4 w-4" />
										</Button>
										<h3
											className={cn(
												'text-sm font-semibold transition-colors duration-200 group-hover:text-foreground text-foreground'
											)}
										>
											{group.name}
										</h3>
										<Badge
											variant="secondary"
											className={cn(
												'text-xs px-2 py-0.5 transition-colors duration-200 bg-muted text-muted-foreground hover:bg-border'
											)}
										>
											{tasks.length}
										</Badge>
									</div>
									<div>
										<DropdownMenu>
											<DropdownMenuTrigger asChild>
												<Button variant="ghost" size="icon">
													<EllipsisVertical className="h-4 w-4" />
												</Button>
											</DropdownMenuTrigger>
											<DropdownMenuContent align="end">
												{actions.map((action) => (
													<DropdownMenuItem
														key={action.value}
														onSelect={action.action}
													>
														{action.label}
													</DropdownMenuItem>
												))}
											</DropdownMenuContent>
										</DropdownMenu>
									</div>
								</div>
							)}
						</div>
					)}
				</CardHeader>

				{!isCollapsed && (
					<>
						<CardContent className="pt-0 p-0">
							<Droppable droppableId={group._id}>
								{(provided, snapshot) => (
									<div
										ref={provided.innerRef}
										{...provided.droppableProps}
										className={cn(
											'min-h-[1px] gap-2 flex flex-col bg-muted/50 rounded-md'
										)}
									>
										{tasks.map((task, index) => (
											<KanbanCard
												key={task._id}
												card={task}
												index={index}
												onClick={handleTaskClick}
												isGlassMode={isGlassMode}
											/>
										))}
										{provided.placeholder}
									</div>
								)}
							</Droppable>
						</CardContent>

						<div className="flex-shrink-0">
							{isAddingTask ? (
								<Card className="bg-card border-border border-2 shadow-sm">
									<CardContent className="p-3">
										<Form {...taskForm}>
											<form
												onSubmit={taskForm.handleSubmit(onSubmitTask)}
												className="space-y-2"
											>
												<FormField
													control={taskForm.control}
													name="name"
													render={({ field }) => (
														<FormItem>
															<FormControl>
																<textarea
																	{...field}
																	placeholder="Enter a title for this task..."
																	className="w-full text-sm border-none outline-none resize-none bg-transparent placeholder-muted-foreground focus:placeholder-foreground transition-colors"
																	rows={2}
																	autoFocus
																	onKeyDown={(e) => {
																		if (e.key === 'Enter' && !e.shiftKey) {
																			e.preventDefault();
																			taskForm.handleSubmit(onSubmitTask)();
																		} else if (e.key === 'Escape') {
																			setIsAddingTask(false);
																			taskForm.reset();
																		}
																	}}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
												<div className="flex items-center gap-2">
													<Button
														type="submit"
														size="sm"
														disabled={!taskForm.watch('name')?.trim()}
														className="h-7 px-3 text-xs bg-primary hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 text-primary-foreground"
													>
														Add task
													</Button>
													<Button
														type="button"
														variant="ghost"
														size="sm"
														onClick={() => {
															setIsAddingTask(false);
															taskForm.reset();
														}}
														className="h-7 px-3 text-xs text-muted-foreground hover:text-foreground transition-colors"
													>
														Cancel
													</Button>
												</div>
											</form>
										</Form>
									</CardContent>
								</Card>
							) : (
								<Button
									variant="ghost"
									className={cn(
										'w-full justify-start text-left p-2 h-auto border-2 border-dashed transition-all duration-200 hover:text-muted-foreground text-muted-foreground border-border hover:border-muted-foreground hover:bg-muted'
									)}
									onClick={() => setIsAddingTask(true)}
								>
									<Plus className="h-4 w-4 mr-2 transition-transform duration-200 group-hover:rotate-90" />
									<span className="text-sm">Add a task</span>
								</Button>
							)}
						</div>
					</>
				)}
			</Card>

			{taskId && tasks.some((t) => t._id === taskId) && (
				<TaskModal
					isOpen={isTaskModalOpen}
					onClose={handleTaskModalClose}
					taskId={taskId}
					onSave={handleTaskSave}
					isGlassMode={isGlassMode}
				/>
			)}
		</div>
	);
};
