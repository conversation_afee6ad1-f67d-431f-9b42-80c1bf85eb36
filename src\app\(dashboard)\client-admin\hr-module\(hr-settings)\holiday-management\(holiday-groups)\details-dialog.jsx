'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogFooter,
} from '@/components/ui/dialog';
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from '@/components/ui/accordion';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	Network,
	Building,
	UserCheck,
	Trash,
	Loader2,
	Pointer,
} from 'lucide-react';
import {
	fetchEmployeeByHolidayGroupId,
	removeEmployeeFromHolidayGroup,
} from '@/lib/features/holiday/holidayGroupSlice';
import { fetchHolidaysByHolidayGroupId } from '@/lib/features/holiday/holidaySlice';
import { icons } from '@/data/icons';

const DetailsDialog = ({
	holidayGroup,
	showDetailsDialog,
	setShowDetailsDialog,
}) => {
	const dispatch = useAppDispatch();
	const { holidayGroupHolidays, isLoading: holidayGroupHolidaysLoading } =
		useAppSelector((store) => store.holiday);
	const { holidayGroupEmployees, isLoading: holidayGroupEmployeesLoading } =
		useAppSelector((store) => store.holidayGroup);
	const [employeeToDelete, setEmployeeToDelete] = useState(null);
	const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
	// Fetch holidays and employees associated with this holiday group
	useEffect(() => {
		if (holidayGroup && showDetailsDialog) {
			dispatch(fetchEmployeeByHolidayGroupId(holidayGroup._id));
			dispatch(fetchHolidaysByHolidayGroupId(holidayGroup._id));
		}
	}, [holidayGroup, showDetailsDialog, dispatch]);

	// Calculate total number of holidays
	const totalHolidays = holidayGroupHolidays?.length;
	// Calculate total days
	const totalDays = holidayGroupHolidays?.reduce(
		(acc, holiday) => acc + holiday.numberOfDays,
		0
	);

	const handleDeleteEmployee = (employee) => {
		setEmployeeToDelete(employee);
		setShowDeleteConfirmation(true);
	};

	const confirmDeleteEmployee = async () => {
		// In a real app, you would dispatch an action to remove the employee from this holiday group
		const payload = {
			holidayGroupId: holidayGroup._id,
			employeeId: employeeToDelete.userId,
		};
		const result = await dispatch(removeEmployeeFromHolidayGroup(payload));

		if (removeEmployeeFromHolidayGroup.fulfilled.match(result)) {
			setShowDeleteConfirmation(false);
			setEmployeeToDelete(null);
		}
	};

	return (
		<>
			<Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
				<DialogContent className="max-w-4xl h-[90vh] gap-0 flex flex-col overflow-hidden p-0">
					<DialogHeader className="p-6">
						<DialogTitle className="text-xl">Holiday Group Details</DialogTitle>
						<DialogDescription>
							Details for holiday group &quot;{holidayGroup?.name}&quot;
						</DialogDescription>
					</DialogHeader>

					<ScrollArea className="flex-1 px-6">
						<div className="space-y-6">
							{/* Holiday Group Accordion */}
							<Accordion
								type="single"
								collapsible
								className="w-full border rounded-md"
							>
								<AccordionItem value="item-1" className="border-0">
									<AccordionTrigger className="px-4 py-2 rounded-md hover:no-underline hover:bg-accent">
										<div className="flex justify-between w-full pr-4">
											<span className="font-medium">{holidayGroup?.name}</span>
											<span className="text-muted-foreground">
												{totalDays} days
											</span>
										</div>
									</AccordionTrigger>
									<AccordionContent className="px-4">
										{holidayGroupHolidaysLoading ? (
											<Loader2 />
										) : (
											<Table className="border-t">
												<TableHeader>
													<TableRow>
														<TableHead>Icon</TableHead>
														<TableHead>Holiday Title</TableHead>
														<TableHead>Start Date</TableHead>
														<TableHead>End Date</TableHead>
														<TableHead className="text-right">
															No. of Days
														</TableHead>
													</TableRow>
												</TableHeader>
												<TableBody>
													{holidayGroupHolidays?.length > 0 ? (
														holidayGroupHolidays.map((holiday) => {
															const holidayIcon = icons.find(
																(icon) => icon.value === holiday.icon
															);
															return (
																<TableRow key={holiday._id}>
																	{holidayIcon ? (
																		<TableCell>
																			<holidayIcon.icon className="h-4 w-4" />
																		</TableCell>
																	) : (
																		<TableCell>
																			<Pointer className="h-4 w-4" />
																		</TableCell>
																	)}

																	<TableCell>{holiday.title}</TableCell>
																	<TableCell>
																		{new Date(
																			holiday.startDate
																		).toLocaleDateString()}
																	</TableCell>
																	<TableCell>
																		{holiday.endDate
																			? new Date(
																					holiday.endDate
																				).toLocaleDateString()
																			: '-'}
																	</TableCell>
																	<TableCell className="text-right">
																		{holiday.numberOfDays}
																	</TableCell>
																</TableRow>
															);
														})
													) : (
														<TableRow>
															<TableCell
																colSpan={6}
																className="text-center py-4"
															>
																No holidays assigned to this holiday group
															</TableCell>
														</TableRow>
													)}
												</TableBody>
											</Table>
										)}
									</AccordionContent>
								</AccordionItem>
							</Accordion>

							{/* Employee Information Section */}
							<div className="mt-6">
								<h3 className="text-lg font-medium mb-2">
									Employees in this Holiday Group
								</h3>
								{holidayGroupEmployeesLoading ? (
									<Loader2 />
								) : (
									<div className="border rounded-md overflow-hidden">
										<Table>
											<TableHeader>
												<TableRow>
													<TableHead>Employee ID</TableHead>
													<TableHead>Name</TableHead>
													<TableHead>Business Unit</TableHead>
													<TableHead>Department</TableHead>
													<TableHead>Designation</TableHead>
													<TableHead className="text-right">Actions</TableHead>
												</TableRow>
											</TableHeader>
											<TableBody>
												{holidayGroupEmployees?.length > 0 ? (
													holidayGroupEmployees.map((employee) => (
														<TableRow key={employee.userId}>
															<TableCell>
																{employee.userId.slice(
																	employee.userId.length - 6
																)}
															</TableCell>
															<TableCell>{employee.employeeName}</TableCell>
															<TableCell>
																<div className="flex items-center gap-2">
																	<Network className="size-4 text-muted-foreground" />
																	<span>
																		{`${employee.businessUnitName} -
																			${employee.businessUnitLocation}` || 'N/A'}
																	</span>
																</div>
															</TableCell>
															<TableCell>
																<div className="flex items-center gap-2">
																	<Building className="size-4 text-muted-foreground" />
																	<span>
																		{employee.departmentName || 'N/A'}
																	</span>
																</div>
															</TableCell>
															<TableCell>
																<div className="flex items-center gap-2">
																	<UserCheck className="size-4 text-muted-foreground" />
																	<span>
																		{employee.designationName || 'N/A'}
																	</span>
																</div>
															</TableCell>
															<TableCell className="text-right">
																<Button
																	variant="ghost"
																	size="icon"
																	className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
																	onClick={() => handleDeleteEmployee(employee)}
																>
																	<Trash className="h-4 w-4" />
																	<span className="sr-only">
																		Delete employee
																	</span>
																</Button>
															</TableCell>
														</TableRow>
													))
												) : (
													<TableRow>
														<TableCell colSpan={6} className="text-center py-4">
															No employees assigned to this holiday group
														</TableCell>
													</TableRow>
												)}
											</TableBody>
										</Table>
									</div>
								)}
							</div>
						</div>
					</ScrollArea>

					<DialogFooter className="p-6 mt-auto">
						<Button onClick={() => setShowDetailsDialog(false)}>Close</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Delete Confirmation Dialog */}
			<Dialog
				open={showDeleteConfirmation}
				onOpenChange={setShowDeleteConfirmation}
			>
				<DialogContent className="sm:max-w-md">
					<DialogHeader>
						<DialogTitle>Remove Employee</DialogTitle>
						<DialogDescription>
							Are you sure you want to remove {employeeToDelete?.name} from this
							holiday group?
						</DialogDescription>
					</DialogHeader>
					<DialogFooter className="flex items-center gap-2 space-x-2 sm:space-x-0">
						<Button
							variant="outline"
							onClick={() => setShowDeleteConfirmation(false)}
						>
							Cancel
						</Button>
						<Button variant="destructive" onClick={confirmDeleteEmployee}>
							Remove
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
};

export default DetailsDialog;
