import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Check, ChevronsUpDown, X, Plus } from 'lucide-react';
import {
	addHolidayGroup,
	updateHolidayGroup,
} from '@/lib/features/holiday/holidayGroupSlice';
import { fetchPopulatedBusinessUnits } from '@/lib/features/company-infrastructure/businessUnitSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { addUpdateHolidayGroupSchema } from '@/lib/schemas/companySchema';
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useEffect, useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from '@/components/ui/command';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';

const HolidayGroupAddEditDialog = ({
	isAdd,
	title,
	desc,
	holidayGroup,
	showAddEditDialog,
	setShowAddEditDialog,
}) => {
	const dispatch = useAppDispatch();
	const { populatedBusinessUnits: businessUnits } = useAppSelector(
		(store) => store.businessUnit
	);

	// State to store hierarchical data to show initially in edit mode
	const [initialDepartments, setInitialDepartments] = useState([]);
	const [initialDesignations, setInitialDesignations] = useState([]);
	const [initialEmployees, setInitialEmployees] = useState([]);

	const form = useForm({
		resolver: zodResolver(addUpdateHolidayGroupSchema),
		mode: 'onChange',

		defaultValues: isAdd
			? {
					name: '',
					businessUnit: [],
					department: [],
					designation: [],
					employee: [],
				}
			: {
					_id: '',
					name: '',
					businessUnit: [],
					department: [],
					designation: [],
					employee: [],
				},

		mode: 'onSubmit',
		reValidateMode: 'onSubmit',
	});

	useEffect(() => {
		if (businessUnits.length === 0) {
			dispatch(fetchPopulatedBusinessUnits());
		}
	}, [businessUnits.length, dispatch]);

	useEffect(() => {
		if (!isAdd && holidayGroup && businessUnits.length > 0) {
			form.setValue('_id', holidayGroup._id);
			form.setValue('name', holidayGroup.name);
			form.setValue('businessUnit', holidayGroup.assignment.businessUnit || []);
			form.setValue('department', holidayGroup.assignment.department || []);
			form.setValue('designation', holidayGroup.assignment.designation || []);
			form.setValue('employee', holidayGroup.assignment.employee || []);
		}
	}, [isAdd, holidayGroup, form, businessUnits, businessUnits.length]);

	const watchBusinessUnit = useWatch({
		control: form.control,
		name: 'businessUnit',
	});

	const watchDepartment = useWatch({
		control: form.control,
		name: 'department',
	});

	const watchDesignation = useWatch({
		control: form.control,
		name: 'designation',
	});

	// Reset hierarchy when higher level selections change
	useEffect(() => {
		if (watchBusinessUnit && watchBusinessUnit.length > 0) {
			// Only reset if the user actively changes the selection
			if (document.activeElement?.name === 'businessUnit') {
				form.setValue('department', []);
				form.setValue('designation', []);
				form.setValue('employee', []);
			}
		}
	}, [watchBusinessUnit, form]);

	useEffect(() => {
		if (watchDepartment && watchDepartment.length > 0) {
			// Only reset if the user actively changes the selection
			if (document.activeElement?.name === 'department') {
				form.setValue('designation', []);
				form.setValue('employee', []);
			}
		}
	}, [watchDepartment, form]);

	useEffect(() => {
		if (watchDesignation && watchDesignation.length > 0) {
			// Only reset if the user actively changes the selection
			if (document.activeElement?.name === 'designation') {
				form.setValue('employee', []);
			}
		}
	}, [watchDesignation, form]);

	// Helper functions for hierarchy management
	const getDepartmentsForBusinessUnits = (selectedBusinessUnits) => {
		if (!selectedBusinessUnits?.length) return [];

		if (selectedBusinessUnits.includes('all')) {
			return businessUnits.flatMap((bu) => bu.departments || []);
		}

		return businessUnits
			.filter((bu) => selectedBusinessUnits.includes(bu._id))
			.flatMap((bu) => bu.departments || []);
	};

	const getDesignationsForDepartments = (selectedDepartments) => {
		if (!selectedDepartments?.length) return [];

		const departments = getDepartmentsForBusinessUnits(
			form.getValues('businessUnit')
		);
		if (selectedDepartments.includes('all')) {
			return departments.flatMap((dept) => dept.designations || []);
		}

		return departments
			.filter((dept) => selectedDepartments.includes(dept._id))
			.flatMap((dept) => dept.designations || []);
	};

	const getEmployeesForDesignations = (selectedDesignations) => {
		if (!selectedDesignations?.length) return [];

		const designations = getDesignationsForDepartments(
			form.getValues('department')
		);
		if (selectedDesignations.includes('all')) {
			return designations.flatMap((desig) => desig.employees || []);
		}

		return designations
			.filter((desig) => selectedDesignations.includes(desig._id))
			.flatMap((desig) => desig.employees || []);
	};

	// Helper function to get options for dropdowns
	const getOptions = (type, selectedValues) => {
		switch (type) {
			case 'businessUnit':
				return [
					{ value: 'all', label: 'All Business Units' },
					...businessUnits
						.filter((bu) => bu._id)
						.map((bu) => ({
							value: bu._id,
							label: `${bu.name} - ${bu.location}`,
						})),
				];
			case 'department':
				const departments = getDepartmentsForBusinessUnits(selectedValues);
				return [
					{ value: 'all', label: 'All Departments' },
					...departments
						.filter((dept) => dept._id)
						.map((dept) => ({
							value: dept._id,
							label: dept.name,
						})),
				];
			case 'designation':
				const designations = getDesignationsForDepartments(selectedValues);
				return [
					{ value: 'all', label: 'All Designations' },
					...designations
						.filter((desig) => desig._id)
						.map((desig) => ({
							value: desig._id,
							label: desig.name,
						})),
				];
			case 'employee':
				const employees = getEmployeesForDesignations(selectedValues);
				return [
					{ value: 'all', label: 'All Employees' },
					...employees
						.filter((emp) => emp.userId)
						.map((emp) => ({
							value: emp.userId,
							label: emp.userName,
						})),
				];
			default:
				return [];
		}
	};

	// Helper function to get label text for dropdowns
	const getSelectLabel = (selectedValues, options) => {
		if (!selectedValues?.length) return 'Select...';
		if (selectedValues.includes('all')) return 'All Selected';
		return `${selectedValues.length} items Selected`;
	};

	// Helper function to render selected badges
	const renderSelectedBadges = (selectedValues, options, onRemove) => {
		if (!selectedValues?.length) return null;

		const itemsToShow = selectedValues.includes('all')
			? options.filter((opt) => opt.value !== 'all')
			: selectedValues
					.map((value) => options.find((opt) => opt.value === value))
					.filter(Boolean);

		return (
			<div className="flex flex-wrap gap-1 mt-2">
				{itemsToShow.map((item) => (
					<Badge
						key={item.value}
						variant="secondary"
						className="flex items-center gap-1"
					>
						{item.label}
						<button
							type="button"
							onClick={() => onRemove(item.value)}
							className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
						>
							<X className="h-3 w-3" />
							<span className="sr-only">Remove {item.label}</span>
						</button>
					</Badge>
				))}
			</div>
		);
	};

	// Handle hierarchy changes
	const handleBusinessUnitChange = (newValue) => {
		// Clear all dependent selections when business unit changes
		form.setValue('department', []);
		form.setValue('designation', []);
		form.setValue('employee', []);
	};

	const handleDepartmentChange = (newValue) => {
		// Clear designation and employee selections when department changes
		form.setValue('designation', []);
		form.setValue('employee', []);
	};

	const handleDesignationChange = (newValue) => {
		// Clear employee selections when designation changes
		form.setValue('employee', []);
	};

	// Handle selection changes
	const handleSelectionChange = (field, value) => {
		const currentValue = form.getValues(field) || [];
		let newValue;

		if (value === 'all') {
			newValue = ['all'];
		} else if (currentValue.includes('all')) {
			newValue = [value];
		} else if (currentValue.includes(value)) {
			newValue = currentValue.filter((v) => v !== value);
		} else {
			newValue = [...currentValue, value];
		}

		form.setValue(field, newValue);

		// Handle hierarchy changes
		switch (field) {
			case 'businessUnit':
				handleBusinessUnitChange(newValue);
				break;
			case 'department':
				handleDepartmentChange(newValue);
				break;
			case 'designation':
				handleDesignationChange(newValue);
				break;
		}
	};

	// Handle badge removal
	const handleBadgeRemove = (field, value) => {
		const currentValue = form.getValues(field) || [];
		const newValue = currentValue.filter((v) => v !== value);
		form.setValue(field, newValue);

		// Handle hierarchy changes
		switch (field) {
			case 'businessUnit':
				handleBusinessUnitChange(newValue);
				break;
			case 'department':
				handleDepartmentChange(newValue);
				break;
			case 'designation':
				handleDesignationChange(newValue);
				break;
		}
	};

	// Initialize form with existing values
	useEffect(() => {
		if (holidayGroup?.assignment) {
			form.setValue('businessUnit', holidayGroup.assignment.businessUnit || []);
			form.setValue('department', holidayGroup.assignment.department || []);
			form.setValue('designation', holidayGroup.assignment.designation || []);
			form.setValue('employee', holidayGroup.assignment.employee || []);
		}
	}, [holidayGroup, form]);

	const onSubmit = async (data) => {
		const assignment = {
			businessUnit: [],
			department: [],
			designation: [],
			employee: [],
		};
		let label = '';
		let valueIds = [];

		if (data.businessUnit && data.businessUnit.length > 0) {
			label = 'businessUnit';
			if (data.businessUnit.includes('all')) {
				valueIds = businessUnits.map((bu) => bu._id);
			} else {
				valueIds = data.businessUnit;
			}
			assignment[label] = valueIds;
		}
		if (data.department && data.department.length > 0) {
			label = 'department';
			if (data.department.includes('all')) {
				const departments = getDepartmentsForBusinessUnits(data.businessUnit);
				valueIds = departments.map((dept) => dept._id);
			} else {
				valueIds = data.department;
			}
			assignment[label] = valueIds;
		}
		if (data.designation && data.designation.length > 0) {
			label = 'designation';
			if (data.designation.includes('all')) {
				const designations = getDesignationsForDepartments(data.department);
				valueIds = designations.map((des) => des._id);
			} else {
				valueIds = data.designation;
			}
			assignment[label] = valueIds;
		}
		if (data.employee && data.employee.length > 0) {
			label = 'employee';
			if (data.employee.includes('all')) {
				const employees = getEmployeesForDesignations(data.designation);
				valueIds = employees.map((emp) => emp.userId);
			} else {
				valueIds = data.employee;
			}
			assignment[label] = valueIds;
		}

		const transformedData = {
			name: data.name,
			assignment,
			assignTo: {
				label: label,
				value: valueIds,
			},
		};

		if (!isAdd && data._id) {
			transformedData._id = data._id;
		}

		const result = isAdd
			? await dispatch(addHolidayGroup(transformedData))
			: await dispatch(updateHolidayGroup(transformedData));

		if (
			addHolidayGroup.fulfilled.match(result) ||
			updateHolidayGroup.fulfilled.match(result)
		) {
			setShowAddEditDialog(false);
		}
	};

	return (
		<Dialog open={showAddEditDialog} onOpenChange={setShowAddEditDialog}>
			<DialogContent className="max-h-[80vh] overflow-hidden">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>{desc}</DialogDescription>
				</DialogHeader>
				<Form {...form}>
					<form
						id="holiday-group-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="space-y-4 max-h-[50vh] overflow-y-auto p-2"
					>
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Name</FormLabel>
									<FormControl>
										<Input {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Business Unit Select */}
						<FormField
							control={form.control}
							name="businessUnit"
							render={({ field }) => (
								<FormItem className="flex flex-col">
									<FormLabel>Business Unit</FormLabel>
									<Popover>
										<PopoverTrigger asChild>
											<FormControl>
												<Button
													variant="outline"
													role="combobox"
													className={cn(
														'w-full justify-between',
														!field.value?.length && 'text-muted-foreground'
													)}
												>
													{getSelectLabel(
														field.value,
														getOptions('businessUnit')
													)}
													<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
												</Button>
											</FormControl>
										</PopoverTrigger>
										<PopoverContent className="w-full p-0" align="start">
											<Command>
												<CommandInput placeholder="Search business units..." />
												<CommandList>
													<CommandEmpty>No business unit found.</CommandEmpty>
													<CommandGroup>
														{getOptions('businessUnit').map((option) => (
															<CommandItem
																key={option.value}
																value={option.label}
																onSelect={() => {
																	handleSelectionChange(
																		'businessUnit',
																		option.value
																	);
																}}
															>
																<Check
																	className={cn(
																		'mr-2 h-4 w-4',
																		field.value?.includes(option.value)
																			? 'opacity-100'
																			: 'opacity-0'
																	)}
																/>
																{option.label}
															</CommandItem>
														))}
													</CommandGroup>
												</CommandList>
											</Command>
										</PopoverContent>
									</Popover>
									{renderSelectedBadges(
										field.value,
										getOptions('businessUnit'),
										(value) => {
											handleBadgeRemove('businessUnit', value);
										}
									)}
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Department Select */}
						<FormField
							control={form.control}
							name="department"
							render={({ field }) => (
								<FormItem className="flex flex-col">
									<FormLabel>Department (Optional)</FormLabel>
									<Popover>
										<PopoverTrigger asChild>
											<FormControl>
												<Button
													variant="outline"
													role="combobox"
													className={cn(
														'w-full justify-between',
														!field.value?.length && 'text-muted-foreground'
													)}
													disabled={!watchBusinessUnit?.length}
												>
													{getSelectLabel(
														field.value,
														getOptions(
															'department',
															form.getValues('businessUnit')
														)
													)}
													<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
												</Button>
											</FormControl>
										</PopoverTrigger>
										<PopoverContent className="w-full p-0" align="start">
											<Command>
												<CommandInput placeholder="Search departments..." />
												<CommandList>
													<CommandEmpty>No department found.</CommandEmpty>
													<CommandGroup>
														{getOptions(
															'department',
															form.getValues('businessUnit')
														).map((option) => (
															<CommandItem
																key={option.value}
																value={option.label}
																onSelect={() => {
																	handleSelectionChange(
																		'department',
																		option.value
																	);
																}}
															>
																<Check
																	className={cn(
																		'mr-2 h-4 w-4',
																		field.value?.includes(option.value)
																			? 'opacity-100'
																			: 'opacity-0'
																	)}
																/>
																{option.label}
															</CommandItem>
														))}
													</CommandGroup>
												</CommandList>
											</Command>
										</PopoverContent>
									</Popover>
									{renderSelectedBadges(
										field.value,
										getOptions('department', form.getValues('businessUnit')),
										(value) => {
											handleBadgeRemove('department', value);
										}
									)}
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Designation Select */}
						<FormField
							control={form.control}
							name="designation"
							render={({ field }) => (
								<FormItem className="flex flex-col">
									<FormLabel>Designation (Optional)</FormLabel>
									<Popover>
										<PopoverTrigger asChild>
											<FormControl>
												<Button
													variant="outline"
													role="combobox"
													className={cn(
														'w-full justify-between',
														!field.value?.length && 'text-muted-foreground'
													)}
													disabled={!watchDepartment?.length}
												>
													{getSelectLabel(
														field.value,
														getOptions(
															'designation',
															form.getValues('department')
														)
													)}
													<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
												</Button>
											</FormControl>
										</PopoverTrigger>
										<PopoverContent className="w-full p-0" align="start">
											<Command>
												<CommandInput placeholder="Search designations..." />
												<CommandList>
													<CommandEmpty>No designation found.</CommandEmpty>
													<CommandGroup>
														{getOptions(
															'designation',
															form.getValues('department')
														).map((option) => (
															<CommandItem
																key={option.value}
																value={option.label}
																onSelect={() => {
																	handleSelectionChange(
																		'designation',
																		option.value
																	);
																}}
															>
																<Check
																	className={cn(
																		'mr-2 h-4 w-4',
																		field.value?.includes(option.value)
																			? 'opacity-100'
																			: 'opacity-0'
																	)}
																/>
																{option.label}
															</CommandItem>
														))}
													</CommandGroup>
												</CommandList>
											</Command>
										</PopoverContent>
									</Popover>
									{renderSelectedBadges(
										field.value,
										getOptions('designation', form.getValues('department')),
										(value) => {
											handleBadgeRemove('designation', value);
										}
									)}
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Employee Select */}
						<FormField
							control={form.control}
							name="employee"
							render={({ field }) => (
								<FormItem className="flex flex-col">
									<FormLabel>Employee (Optional)</FormLabel>
									<Popover>
										<PopoverTrigger asChild>
											<FormControl>
												<Button
													variant="outline"
													role="combobox"
													className={cn(
														'w-full justify-between',
														!field.value?.length && 'text-muted-foreground'
													)}
													disabled={!watchDesignation?.length}
												>
													{getSelectLabel(
														field.value,
														getOptions(
															'employee',
															form.getValues('designation')
														)
													)}
													<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
												</Button>
											</FormControl>
										</PopoverTrigger>
										<PopoverContent className="w-full p-0" align="start">
											<Command>
												<CommandInput placeholder="Search employees..." />
												<CommandList>
													<CommandEmpty>No employee found.</CommandEmpty>
													<CommandGroup>
														{getOptions(
															'employee',
															form.getValues('designation')
														).map((option) => (
															<CommandItem
																key={option.value}
																value={option.label}
																onSelect={() => {
																	handleSelectionChange(
																		'employee',
																		option.value
																	);
																}}
															>
																<Check
																	className={cn(
																		'mr-2 h-4 w-4',
																		field.value?.includes(option.value)
																			? 'opacity-100'
																			: 'opacity-0'
																	)}
																/>
																{option.label}
															</CommandItem>
														))}
													</CommandGroup>
												</CommandList>
											</Command>
										</PopoverContent>
									</Popover>
									{renderSelectedBadges(
										field.value,
										getOptions('employee', form.getValues('designation')),
										(value) => {
											handleBadgeRemove('employee', value);
										}
									)}
									<FormMessage />
								</FormItem>
							)}
						/>
					</form>
				</Form>
				<DialogFooter>
					<Button variant="outline" onClick={() => setShowAddEditDialog(false)}>
						Cancel
					</Button>
					<Button type="submit" form="holiday-group-form">
						Confirm
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default HolidayGroupAddEditDialog;
