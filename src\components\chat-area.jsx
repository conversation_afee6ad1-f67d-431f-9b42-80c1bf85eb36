import { useEffect, useRef, useState } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Check, CheckCheck, ChevronDown, Clock } from 'lucide-react';
import { Badge } from './ui/badge';

import InputArea from './input-area';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	getMessages,
	markChatAsRead,
	markMessageAsRead,
	receiveMessage,
} from '@/lib/features/chat/chatSlice';

import { getInitials } from '@/lib/utils';
import dayjs from 'dayjs';
import io from 'socket.io-client';

// const socket = io('https://tms-backend-muzr.onrender.com/chat', {
// 	withCredentials: true,
// }); // staging

  const socket = io('https://harp-hr-backend.onrender.com/chat', {
  	withCredentials: true,
  }); // production

export function ChatArea() {
	const dispatch = useAppDispatch();
	const { selectedChat, messages, selectedChatUser } = useAppSelector(
		(store) => store.chat
	);
	const { authenticatedUser: user } = useAppSelector((store) => store.auth);

	// Refs and state
	const scrollAreaRef = useRef(null);
	const messagesEndRef = useRef(null);
	const dateRefs = useRef({});
	const scrollTimeout = useRef(null);

	const [otherUser, setOtherUser] = useState(null);
	const [floatingDate, setFloatingDate] = useState('');
	const [isNearBottom, setIsNearBottom] = useState(true);
	const [showScrollButton, setShowScrollButton] = useState(false);

	// Utils
	const getDateLabel = (date) => {
		const today = dayjs();
		const msgDate = dayjs(date);

		if (msgDate.isSame(today, 'day')) return 'Today';
		if (msgDate.isSame(today.subtract(1, 'day'), 'day')) return 'Yesterday';
		return msgDate.format('D MMM YYYY');
	};

	const scrollToBottom = (behavior = 'smooth') => {};

	// Set other user info when chat changes
	useEffect(() => {
		if (!selectedChatUser) return;
		const participant = selectedChatUser.participants?.find(
			(p) => p?.userId !== user?.userId
		);
		setOtherUser(participant || null);
	}, [selectedChatUser, user?.userId]);

	// Floating date + scroll tracking
	const handleScroll = () => {
		if (!scrollAreaRef.current) return;

		clearTimeout(scrollTimeout.current);
		scrollTimeout.current = setTimeout(() => setFloatingDate(''), 1000);

		let currentDate = '';
		let minDistance = Number.POSITIVE_INFINITY;
		const containerTop = scrollAreaRef.current.getBoundingClientRect().top;

		let showFloat = true;

		for (const [label, el] of Object.entries(dateRefs.current)) {
			if (!el) continue;
			const distanceFromTop = el.getBoundingClientRect().top - containerTop;

			if (distanceFromTop < 20 && Math.abs(distanceFromTop) < minDistance) {
				minDistance = Math.abs(distanceFromTop);
				currentDate = label;

				// // 👇 If it's within 50px from top, hide the float
				// if (distanceFromTop < 50 && distanceFromTop > 5) {
				// 	showFloat = false;
				// }
			}
		}

		setFloatingDate(showFloat ? currentDate : '');

		const { scrollHeight, scrollTop, clientHeight } = scrollAreaRef.current;
		const nearBottom = scrollHeight - scrollTop - clientHeight < 100;
		setIsNearBottom(nearBottom);
		setShowScrollButton(!nearBottom);
	};

	// Fetch messages + socket setup when chat changes
	useEffect(() => {
		if (!selectedChat) return;

		dispatch(getMessages(selectedChat));
		socket.emit('joinChat', selectedChat);

		// Reset UI state
		setFloatingDate('');
		dateRefs.current = {};
		setIsNearBottom(true);

		// Socket listeners
		const handleNewMessage = (message) => {
			dispatch(receiveMessage(message));
			if (
				selectedChat === message.chat &&
				message?.sender?._id !== user.userId
			) {
				dispatch(markChatAsRead(message._id));
			}
		};

		const handleMessageRead = (message) => {
			dispatch(markMessageAsRead(message.messageId));
		};

		socket.on('newMessage', handleNewMessage);
		socket.on('messageRead', handleMessageRead);

		return () => {
			socket.off('newMessage', handleNewMessage);
			socket.off('messageRead', handleMessageRead);
		};
	}, [selectedChat, dispatch, user.userId]);

	// Auto-scroll when messages update
	useEffect(() => {
		if (isNearBottom) scrollToBottom('auto');
		dateRefs.current = {};
		setTimeout(() => handleScroll(), 100); // 💡 Ensure floating date is recalculated after render
	}, [messages]);

	// Also scroll and reset on chat change
	useEffect(() => {
		setFloatingDate('');
		scrollToBottom('auto');
		setTimeout(() => handleScroll(), 100); // 💡 Force scroll recalculation for short chats
	}, [selectedChat]);

	// Early return if no chat selected
	if (!selectedChat) {
		return (
			<div className="flex-grow flex items-center justify-center bg-muted h-[calc(100vh-11rem)] rounded-xl">
				<p className="text-muted-foreground">
					Select a chat to start messaging
				</p>
			</div>
		);
	}

	// Group messages by date
	const groupedMessages = messages.reduce((acc, msg) => {
		const label = getDateLabel(msg.createdAt);
		if (!acc[label]) acc[label] = [];
		acc[label].push(msg);
		return acc;
	}, {});

	return (
		<div className="flex flex-col h-[calc(100vh-11rem)] relative">
			{/* Floating Date Badge */}
			{floatingDate && (
				<div className="absolute top-2 left-1/2 transform -translate-x-1/2 z-20">
					<Badge
						variant="outline"
						className="bg-background text-xs px-4 py-1 shadow-md"
					>
						{floatingDate}
					</Badge>
				</div>
			)}

			{/* Scrollable Chat Area */}
			<ScrollArea
				className="flex-grow p-4"
				onScrollCapture={handleScroll}
				ref={scrollAreaRef}
			>
				<div className="space-y-4">
					{Object.entries(groupedMessages).map(([dateLabel, msgs]) => (
						<div
							key={dateLabel}
							ref={(el) => {
								if (el) dateRefs.current[dateLabel] = el;
							}}
						>
							{/* Static Date Badge */}
							<div className="flex justify-center my-4">
								<Badge
									variant="outline"
									className="bg-background text-xs px-4 py-1 shadow-md"
								>
									{dateLabel}
								</Badge>
							</div>

							{msgs.map((message) => {
								const isSender = message?.sender === user?.userId;

								return (
									<div
										key={message._id}
										className={`flex my-1 gap-1 ${isSender ? 'justify-end' : 'justify-start'}`}
									>
										<div
											className={`flex ${isSender ? 'flex-row-reverse' : 'flex-row'} items-end space-x-2 gap-1`}
										>
											<Avatar className="w-8 h-8">
												<AvatarImage
													src={isSender ? undefined : otherUser?.profilePhoto}
												/>
												<AvatarFallback className="text-xs">
													{isSender ? 'You' : getInitials(otherUser?.name)}
												</AvatarFallback>
											</Avatar>

											<div className="rounded-lg px-3 py-1 max-w-md bg-muted">
												<p>{message.content}</p>
												<div className="flex items-center justify-between gap-4">
													<p className="text-xs text-muted-foreground">
														{dayjs(message.createdAt).format('h:mm a')}
													</p>
													{isSender &&
														(message.status === 'pending' ? (
															<Clock className="h-3 w-3 ml-4 text-muted-foreground" />
														) : message.isRead ? (
															<CheckCheck className="h-4 w-4 ml-4 text-blue-400" />
														) : (
															<Check className="h-4 w-4 ml-4" />
														))}
												</div>
											</div>
										</div>
									</div>
								);
							})}
						</div>
					))}
					<div ref={messagesEndRef} />
				</div>
			</ScrollArea>

			{/* Scroll-to-bottom Button */}
			{showScrollButton && (
				<Button
					size="icon"
					variant="secondary"
					className="absolute bottom-20 right-4 rounded-full shadow-lg"
					onClick={() => scrollToBottom()}
				>
					<ChevronDown className="h-4 w-4" />
				</Button>
			)}

			{/* Input Area */}
			<InputArea />
		</div>
	);
}
