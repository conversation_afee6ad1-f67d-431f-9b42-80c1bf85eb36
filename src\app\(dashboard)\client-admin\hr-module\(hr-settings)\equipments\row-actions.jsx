'use client';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { MoreHorizontal, Edit, Trash } from 'lucide-react';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import AddEditDialog from './add-edit-dialog';
import { deleteEquipment } from '@/lib/features/company-infrastructure/equipmentSlice';

export function DataTableRowActions({ row, dispatch }) {
	const equipment = row.original;
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [showAddEditDialog, setShowAddEditDialog] = useState(false);
	const [selectedEquipment, setSelectedEquipment] = useState(null);

	const handleEdit = () => {
		setSelectedEquipment(equipment);
		setShowAddEditDialog(true);
	};

	const handleDelete = async () => {
		const result = await dispatch(deleteEquipment([equipment._id]));

		if (deleteEquipment.fulfilled.match(result)) {
			setShowDeleteDialog(false);
		}
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" className="h-8 w-8 p-0">
						<span className="sr-only">Open menu</span>
						<MoreHorizontal className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Actions</DropdownMenuLabel>
					<DropdownMenuItem onClick={handleEdit}>
						<Edit className="mr-2 h-4 w-4" />
						Edit
					</DropdownMenuItem>

					<DropdownMenuSeparator />
					<DropdownMenuItem
						onClick={() => setShowDeleteDialog(true)}
						className="text-destructive focus:text-destructive"
					>
						<Trash className="mr-2 h-4 w-4" />
						Delete
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>

			<Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							Are you sure you want to delete this equipment?
						</DialogTitle>
						<DialogDescription>
							This action cannot be undone. This will permanently delete the
							equipment record and all associated data.
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowDeleteDialog(false)}
						>
							Cancel
						</Button>
						<Button variant="destructive" onClick={handleDelete}>
							Delete
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Edit Equipment Dialog */}
			{showAddEditDialog && (
				<AddEditDialog
					isAdd={false}
					title="Edit Equipment"
					desc="Edit equipment details"
					equipment={selectedEquipment}
					showAddEditDialog={showAddEditDialog}
					setShowAddEditDialog={setShowAddEditDialog}
				/>
			)}
		</>
	);
}
