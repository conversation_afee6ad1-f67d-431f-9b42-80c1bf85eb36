'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MoreHorizontal, Store } from 'lucide-react';
import { cn, userRoles } from '@/lib/utils';
import { ProjectContextMenu } from './ProjectContextMenu';
import { useAppSelector } from '@/lib/hooks';

/**
 * ProjectCard Component
 * Displays a project card with background image/color and minimal content
 * Features: hover effects, context menu, responsive design
 */
export const ProjectCard = ({
	project,
	onClick,
	onEdit,
	onDelete,
	onViewSummary,
}) => {
	const { authenticatedUser } = useAppSelector((store) => store.auth);
	const [showContextMenu, setShowContextMenu] = useState(false);
	const [isHovered, setIsHovered] = useState(false);

	const handleCardClick = (e) => {
		// Prevent card click when context menu is clicked
		if (e.target.closest('[data-context-menu]')) {
			return;
		}
		onClick?.(project);
	};

	const handleContextMenuClick = (e) => {
		e.stopPropagation();
		setShowContextMenu(!showContextMenu);
	};

	const getBackgroundStyle = () => {
		if (project.bgType === 'image' && project.bgImage) {
			return {
				backgroundImage: `url(${project.bgImage.thumbnail})`,
				backgroundSize: 'cover',
				backgroundPosition: 'center',
				backgroundRepeat: 'no-repeat',
				// Add loading optimization
				backgroundAttachment: 'local',
			};
		} else {
			return {};
		}
	};

	const getBackgroundClass = () => {
		if (project.bgType === 'color') {
			return project.bgColor;
		}
		return '';
	};

	return (
		<Card
			className={cn(
				'group cursor-pointer transition-all duration-300 overflow-hidden relative',
				'hover:shadow-xl hover:scale-[1.02]',
				'aspect-video', // 16:9 aspect ratio
				// 'bg-[#cccccc]'
				getBackgroundClass()
			)}
			style={getBackgroundStyle()}
			onClick={handleCardClick}
			onMouseEnter={() => setIsHovered(true)}
			onMouseLeave={() => setIsHovered(false)}
		>
			{/* Context Menu Button - Only visible on hover */}
			{authenticatedUser.role <= userRoles.CLIENT_ADMIN ||
			authenticatedUser.userId === project?.createdBy ? (
				<div
					className={cn(
						'absolute top-3 right-3 z-10 transition-opacity duration-200',
						isHovered ? 'opacity-100' : 'opacity-0'
					)}
					data-context-menu
				>
					<ProjectContextMenu
						project={project}
						onEdit={onEdit}
						onDelete={onDelete}
						onViewSummary={onViewSummary}
						onView={onClick}
					>
						<Button
							variant="secondary"
							size="sm"
							className="h-8 w-8 p-0 shadow-sm"
							onClick={handleContextMenuClick}
						>
							<MoreHorizontal className="h-4 w-4" />
						</Button>
					</ProjectContextMenu>
				</div>
			) : (
				<></>
			)}

			{/* Content Overlay - Bottom Left */}
			<div className="absolute bottom-0 left-0 right-0 p-4 bg-black/40">
				{/* Gradient Overlay for Text Readability */}
				<div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent rounded-b-lg" />

				{/* Project Information */}
				<div className="relative z-10 text-white">
					<h3 className="font-semibold text-lg mb-1 line-clamp-1">
						{project.name.charAt(0).toUpperCase() + project.name.slice(1)}
					</h3>
					{/* <p className="text-sm text-white/90 line-clamp-2 drop-shadow-sm">
						{project.description}
					</p> */}
				</div>
			</div>

			{/* Hover Overlay Effect */}
			<div
				className={cn(
					'absolute inset-0 bg-black/10 transition-opacity duration-200',
					isHovered ? 'opacity-100' : 'opacity-0'
				)}
			/>
		</Card>
	);
};
