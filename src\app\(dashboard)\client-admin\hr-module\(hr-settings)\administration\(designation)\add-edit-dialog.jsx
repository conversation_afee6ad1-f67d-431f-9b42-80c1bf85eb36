'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { dummyEmployees } from '@/data/employeesData';
import { fetchDepartments } from '@/lib/features/company-infrastructure/departmentSlice';
import {
	addDesignation,
	updateDesignation,
} from '@/lib/features/company-infrastructure/designationSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { addUpdateDesignationSchema } from '@/lib/schemas/companySchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { XCircle } from 'lucide-react';
import { useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';

const DesignationAddEditDialog = ({
	isAdd,
	title,
	desc,
	designation,
	showAddEditDialog,
	setShowAddEditDialog,
}) => {
	const dispatch = useAppDispatch();
	const { departments } = useAppSelector((store) => store.department);

	const form = useForm({
		resolver: zodResolver(addUpdateDesignationSchema),
		defaultValues: {
			designations: isAdd
				? [{ name: '', departmentId: '' }]
				: [{ _id: '', name: '', departmentId: '' }],
		},
		mode: 'onChange',
		reValidateMode: 'onSubmit',
	});

	const { fields, append, remove } = useFieldArray({
		control: form.control,
		name: 'designations',
	});

	useEffect(() => {
		if (!isAdd && designation) {
			form.setValue('designations', [
				{
					_id: designation._id,
					name: designation.name,
					departmentId: designation.department._id,
				},
			]);
		}
	}, [isAdd, designation, form]);

	useEffect(() => {
		if (departments.length === 0) {
			dispatch(fetchDepartments());
		}
	}, [dispatch, departments.length]);

	const onSubmit = async (data) => {
		let result;
		if (isAdd) {
			const payload = { designations: data.designations };
			result = await dispatch(addDesignation(payload));
		} else {
			const payload = data.designations[0];
			result = await dispatch(updateDesignation(payload));
		}

		if (
			addDesignation.fulfilled.match(result) ||
			updateDesignation.fulfilled.match(result)
		) {
			setShowAddEditDialog(false);
		}
	};

	return (
		<Dialog open={showAddEditDialog} onOpenChange={setShowAddEditDialog}>
			<DialogContent className="max-h-[80vh] overflow-hidden">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>{desc}</DialogDescription>
				</DialogHeader>
				<Form {...form}>
					<form
						id="designation-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="grid gap-4 max-h-[50vh] overflow-y-auto pt-2 pb-2 pr-2"
					>
						{fields.map((designation, index) => (
							<div
								key={designation.id}
								className="relative grid grid-cols-2 gap-4 border p-4 rounded-lg"
							>
								{isAdd && fields.length > 1 && (
									<button
										type="button"
										onClick={() => remove(index)}
										className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200"
									>
										<XCircle className="text-red-500" size={16} />
									</button>
								)}
								<FormField
									control={form.control}
									name={`designations.${index}.name`}
									render={({ field }) => (
										<FormItem>
											<FormLabel>Name</FormLabel>
											<FormControl>
												<Input {...field} placeholder="Enter Designation name" />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name={`designations.${index}.departmentId`}
									render={({ field }) => (
										<FormItem>
											<FormLabel>Department</FormLabel>
											<Select
												onValueChange={field.onChange}
												defaultValue={field.value}
											>
												<SelectTrigger>
													<SelectValue placeholder="Select a department" />
												</SelectTrigger>
												<SelectContent>
													<ScrollArea className="max-h-60 overflow-y-auto">
														<div>
															{departments.map((department) => (
																<SelectItem
																	key={department._id}
																	value={department._id}
																>
																	{department.name} - (
																	{department.businessUnit.name} -{' '}
																	{department.businessUnit.location})
																</SelectItem>
															))}
														</div>
													</ScrollArea>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						))}
						{isAdd && (
							<Button
								type="button"
								variant="outline"
								onClick={() => append({ name: '', departmentId: '' })}
								className="mt-2"
							>
								Add Another Designation
							</Button>
						)}
					</form>
				</Form>
				<DialogFooter>
					<Button variant="outline" onClick={() => setShowAddEditDialog(false)}>
						Cancel
					</Button>
					<Button type="submit" form="designation-form">
						Confirm
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default DesignationAddEditDialog;
