import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Search, PlusIcon, Plus } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { SidebarInput } from '@/components/ui/sidebar';
import {
	getChats,
	createChat,
	getChatUsers,
} from '@/lib/features/chat/chatSlice';
import { fetchAllEmployees } from '@/lib/features/employees/employeeSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { Button } from './ui/button';
import { SelectDialog } from './select-dialog';

const ChatUserListItem = ({ user }) => {
	return (
		<Alert className="w-full flex gap-3 items-center px-2" variant="default">
			<Avatar className="w-8 h-8">
				<AvatarImage src={user.profileImage} alt={user.name} />
				<AvatarFallback>
					{user?.name
						?.split(' ')
						?.map((n) => n[0])
						?.join('')
						?.slice(0, 2)}
				</AvatarFallback>
			</Avatar>
			<div className="flex flex-col w-0 flex-1">
				<AlertTitle className="text-sm font-semibold">{user.name}</AlertTitle>
				<AlertDescription className="truncate text-xs overflow-hidden -mt-1 text-gray-500">
					{user.lastMessage}
				</AlertDescription>
			</div>
		</Alert>
	);
};

const ChatUserList = () => {
	const dispatch = useAppDispatch();

	const [searchTerm, setSearchTerm] = useState('');
	const [chatList, setChatList] = useState([]);

	const { authenticatedUser } = useAppSelector((store) => store.auth);
	const { chatList: data, chatUsers: employees } = useAppSelector(
		(store) => store.chat
	);
	const [showSelectDialog, setShowSelectDialog] = useState(false);

	useEffect(() => {
		dispatch(getChats());
		dispatch(getChatUsers());
	}, [dispatch]);

	useEffect(() => {
		const formattedChatList = data?.map((chat) => {
			let currentParticipant = chat?.participants?.filter(
				(participant) => participant?.userId != authenticatedUser.userId
			);

			return {
				id: chat._id,
				name: currentParticipant[0]?.name,
				profileImage: currentParticipant[0]?.profilePhoto,
				isActive: false,
			};
		});

		setChatList(formattedChatList);
	}, [data]);

	const filteredConversations = chatList?.filter((chatList) => {
		const search = searchTerm?.trim()?.toLowerCase();
		return chatList?.name?.toLowerCase()?.includes(search);
	});

	const handleAddChat = (userId) => {
		let result = dispatch(
			createChat({
				users: [userId],
			})
		);
		setShowSelectDialog(false);
	};

	return (
		<div className="flex flex-col">
			<div className="sticky top-0 z-10 px-1 flex gap-1">
				{/* Use sr-only to hide an element visually without hiding it from screen readers*/}
				<Label htmlFor="search" className="sr-only">
					Search
				</Label>
				<SidebarInput
					id="search"
					placeholder="Search chats..."
					className="pl-8"
					value={searchTerm}
					onChange={(e) => setSearchTerm(e.target.value)}
				/>
				<Search className="pointer-events-none absolute left-3 top-1/2 size-4 -translate-y-1/2 select-none opacity-50" />
				<SelectDialog
					dialogTrigger={
						<Button size="icon" variant="outline" className="h-8 min-w-8">
							<PlusIcon />
						</Button>
					}
					dialogTitle="New Chat"
					dialogDescription="Select a user to start a new chat."
					dialogSubmitText="Start Chat"
					selectLabel="Select User"
					selectItemList={employees.map((emp) => ({
						value: emp._id,
						label: emp?.name,
					}))}
					open={showSelectDialog}
					setOpen={setShowSelectDialog}
					onSubmit={(userId) => handleAddChat(userId)}
				/>
			</div>
			<div className="flex-1 overflow-y-auto p-1 w-full scrollbar-thin">
				{filteredConversations.length > 0 ? (
					filteredConversations.map((user) => (
						<ChatUserListItem key={user.id} user={user} />
					))
				) : (
					<div className="text-center text-sm text-gray-400 py-6">
						No chats found
					</div>
				)}
			</div>
		</div>
	);
};

export default ChatUserList;
