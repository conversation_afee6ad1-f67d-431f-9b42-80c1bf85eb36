import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { customFetch, showErrors } from '@/lib/utils';
import { toast } from 'sonner';

const initialState = {
	departments: [],
	designations: [],
	isLoading: false,
};

export const fetchDepartments = createAsyncThunk(
	'department/fetchDepartments',
	async (params, thunkAPI) => {
		try {
			// console.log('Fetching departments with params:', params);
			const { data } = await customFetch('/departments', {
				withCredentials: true,
			});
			// console.log('Departments API response:', data);
			return data;
		} catch (error) {
			console.error('Error fetching departments:', error);
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateDepartment = createAsyncThunk(
	'department/updateDepartment',
	async (departmentDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/departments',
				departmentDetails
			);

			if (data?.success) {
				thunkAPI.dispatch(fetchDepartments());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const addDepartment = createAsyncThunk(
	'department/addDepartment',
	async (departmentDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/departments',
				departmentDetails
			);

			if (data?.success) {
				thunkAPI.dispatch(fetchDepartments());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const deleteDepartment = createAsyncThunk(
	'department/deleteDepartment',
	async (departmentIds, thunkAPI) => {
		try {
			const { data } = await customFetch.patch('/departments/remove', {
				departmentIds,
			});

			if (data?.success) {
				thunkAPI.dispatch(fetchDepartments());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchDepartmentDesignations = createAsyncThunk(
	'department/fetchDepartmentDesignations',
	async (departmentId, thunkAPI) => {
		try {
			const { data } = await customFetch(
				`/departments/${departmentId}/designations`
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const departmentSlice = createSlice({
	name: 'department',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchDepartments.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchDepartments.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.departments = payload.data.departments;
			})
			.addCase(fetchDepartments.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateDepartment.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Department...');
			})
			.addCase(updateDepartment.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateDepartment.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(addDepartment.pending, (state) => {
				state.isLoading = true;
				toast.info('Creating Department...');
			})
			.addCase(addDepartment.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(addDepartment.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(deleteDepartment.pending, (state) => {
				state.isLoading = true;
				toast.info('Deleting Department...');
			})
			.addCase(deleteDepartment.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(deleteDepartment.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchDepartmentDesignations.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchDepartmentDesignations.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.designations = payload.data;
			})
			.addCase(fetchDepartmentDesignations.rejected, (state, { payload }) => {
				state.isLoading = false;
				state.designations = [];
				showErrors(payload);
			});
	},
});

// export const {} = departmentSlice.actions;
export default departmentSlice.reducer;
