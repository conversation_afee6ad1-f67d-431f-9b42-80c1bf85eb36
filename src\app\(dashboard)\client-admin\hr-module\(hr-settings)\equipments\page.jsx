'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { PlusCircle } from 'lucide-react';
import EquipmentTable from './equipment-table';
import EquipmentAddEditDialog from './add-edit-dialog';

export default function EquipmentsPage() {
	const [showAddEditDialog, setShowAddEditDialog] = useState(true);

	return (
		<div className="container mx-auto py-10 px-2">
			<div className="flex items-center justify-between mb-4">
				<div>
					<h1 className="text-2xl font-bold text-gray-700 dark:text-gray-100">
						Equipments
					</h1>
					<p className="text-sm text-muted-foreground mt-1">
						Manage company equipment and track assignments
					</p>
				</div>
				<Button
					onClick={() => setShowAddEditDialog(true)}
					className="flex items-center gap-2"
				>
					<PlusCircle className="h-4 w-4" /> Add Equipment
				</Button>
			</div>
			<Separator className="my-4" />
			<EquipmentTable />

			{showAddEditDialog && (
				<EquipmentAddEditDialog
					isAdd={true}
					title="Add Equipment"
					desc="Add new equipment to the system"
					showAddEditDialog={showAddEditDialog}
					setShowAddEditDialog={setShowAddEditDialog}
				/>
			)}
		</div>
	);
}
